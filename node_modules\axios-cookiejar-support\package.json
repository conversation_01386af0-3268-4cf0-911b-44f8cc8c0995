{"name": "axios-cookiejar-support", "version": "4.0.7", "description": "Add tough-cookie support to axios.", "keywords": ["axios", "cookie", "cookiejar", "cookies", "tough-cookie"], "homepage": "https://github.com/3846masa/axios-cookiejar-support#readme", "bugs": {"url": "https://github.com/3846masa/axios-cookiejar-support/issues"}, "repository": {"type": "git", "url": "git+https://github.com/3846masa/axios-cookiejar-support.git"}, "funding": "https://github.com/sponsors/3846masa", "license": "MIT", "author": "3846masa <3846mas<PERSON><EMAIL>>", "main": "dist/index.js", "browser": "noop.js", "types": "dist/index.d.ts", "files": ["dist", "noop.js", "!**/__tests__"], "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "format": "npm-run-all format:*", "format:eslint": "eslint --fix --ext .js,.ts,.mjs,.mts .", "format:prettier": "prettier --write .", "lint": "npm-run-all lint:*", "lint:eslint": "eslint --ext .js,.ts,.mjs,.mts .", "lint:prettier": "prettier --check .", "lint:tsc": "tsc --noEmit", "semantic-release": "semantic-release", "pretest": "npm run build", "test": "ava", "patch-package": "patch-package"}, "ava": {"files": ["**/__tests__/*.spec.ts"], "typescript": {"rewritePaths": {"src/": "dist/"}, "compile": false}}, "dependencies": {"http-cookie-agent": "^5.0.4"}, "devDependencies": {"@3846masa/configs": "github:3846masa/configs#cc7782abe6e305c06eb22d5a0152141e226589d1", "@ava/typescript": "4.0.0", "@semantic-release/changelog": "6.0.3", "@semantic-release/exec": "6.0.3", "@semantic-release/git": "10.0.1", "@types/node": "14.18.51", "@types/tough-cookie": "4.0.2", "ava": "5.3.1", "axios": "1.4.0", "npm-run-all": "4.1.5", "patch-package": "7.0.0", "semantic-release": "19.0.5", "tough-cookie": "4.1.3", "typescript": "5.1.3"}, "peerDependencies": {"axios": ">=0.20.0", "tough-cookie": ">=4.0.0"}, "engines": {"node": ">=14.18.0 <15.0.0 || >=16.0.0"}, "publishConfig": {"access": "public"}}