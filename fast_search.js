const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const axios = require('axios');
const { wrapper } = require('axios-cookiejar-support');
const { <PERSON>ieJar } = require('tough-cookie');

const MAX_WORKERS = 1; // Reduced to 1 worker for searcher API reliability

function generateRequestId() {
  return 'f' + Math.random().toString(16).substring(2, 17);
}

if (isMainThread) {
  async function getBackupFolders(client, archiveId) {
    console.log("📁 Discovering backup folders...");

    try {
      const backupsResponse = await client.get("https://cloud-wr-us2.acronis.com/ui/content", {
        headers: {
          "Accept": "application/json, text/plain, */*",
          "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
          "Referer": "https://cloud-wr-us2.acronis.com/",
          "UI-REQUEST-ID": generateRequestId(),
        },
        params: {
          archiveId: archiveId,
          path: ""
        }
      });

      if (!backupsResponse.data || !backupsResponse.data.data) {
        console.log(`⚠️  No data returned for archive ${archiveId}`);
        return [];
      }

      let backupFolders = backupsResponse.data.data.filter(item =>
        item.folderItem && item.name.startsWith("Backup #")
      );

      // Sort backup folders numerically (Backup #1, #2, #3, ..., #10, #11, etc.)
      if (backupFolders.length > 0) {
        backupFolders.sort((a, b) => {
          const na = parseInt(a.name.replace('Backup #', ''), 10);
          const nb = parseInt(b.name.replace('Backup #', ''), 10);
          return na - nb;
        });
        console.log(`📊 Found ${backupFolders.length} backup folders (sorted numerically)`);
      }

      if (backupFolders.length === 0) {
        const driveItems = backupsResponse.data.data.filter(item =>
          item.itemType === "FixedDrive" || (item.folderItem && item.name.match(/^[A-Z0-9-]+:?$/))
        );

        if (driveItems.length > 0) {
          console.log(`📊 Found ${driveItems.length} drive-based backup items (no traditional backup folders)`);
          backupFolders = driveItems.map(drive => ({
            ...drive,
            name: drive.name || drive.displayName || `Drive-${drive.id}`,
            isDriveBased: true
          }));
        }
      }

      console.log(`📊 Total backup folders/drives: ${backupFolders.length}`);
      return backupFolders;
      
    } catch (error) {
      console.log(`❌ Error accessing archive ${archiveId}: ${error.message}`);
      
      if (error.response && error.response.status === 404) {
        console.log(`🔍 Archive appears to be inaccessible (404). Details:`);
        console.log(`   - Archive ID: ${archiveId}`);
        console.log(`   - This might be a .tibx archive or offline archive`);
        console.log(`   - Account might not have access permissions`);
      }
      
      return [];
    }
  }

  async function runParallelSearch(backupFolders, searchKeyword, authCookies, archiveId, accountEmail) {
    console.log(`🚀 Starting parallel search with ${Math.min(MAX_WORKERS, backupFolders.length)} workers...`);

    const results = [];
    const workers = [];
    const maxWorkers = Math.min(MAX_WORKERS, backupFolders.length);

    for (let i = 0; i < backupFolders.length; i += maxWorkers) {
      const batch = backupFolders.slice(i, i + maxWorkers);

      for (const backup of batch) {
        const promise = new Promise((resolve, reject) => {
          const worker = new Worker(__filename, {
            workerData: {
              backup,
              searchKeyword,
              authCookies,
              archiveId,
              accountEmail
            }
          });

          worker.on('message', (message) => {
            if (message.success) {
              console.log(`✅ ${backup.name}: ${message.data.message}`);
              resolve(message.data);
            } else {
              console.log(`❌ ${backup.name}: ${message.error}`);
              resolve({
                backupName: backup.name,
                results: [],
                error: message.error,
                message: `Error: ${message.error}`
              });
            }
            worker.terminate();
          });

          worker.on('error', (error) => {
            console.log(`❌ ${backup.name}: Worker error - ${error.message}`);
            resolve({
              backupName: backup.name,
              results: [],
              error: error.message,
              message: `Worker error: ${error.message}`
            });
          });

          worker.on('exit', (code) => {
            if (code !== 0) {
              console.log(`❌ ${backup.name}: Worker stopped with exit code ${code}`);
              resolve({
                backupName: backup.name,
                results: [],
                error: `Worker exit code ${code}`,
                message: `Worker exit code ${code}`
              });
            }
          });
        });

        workers.push(promise);
      }

      const batchResults = await Promise.all(workers.splice(0, batch.length));
      results.push(...batchResults);

      if (i + maxWorkers < backupFolders.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return results;
  }

  async function searchInArchive(client, cookies, archiveId, searchKeyword, accountEmail, timeout = 900000, progressCallback = null) {
    try {
      console.log(`🔍 [${accountEmail}] Searching archive ${archiveId} for "${searchKeyword}"`);

      const isTibxArchive = archiveId.includes('.tibx');
      if (isTibxArchive) {
        console.log(`📦 [${accountEmail}] Detected .tibx archive format`);
      }

      const backupFolders = await getBackupFolders(client, archiveId);
      console.log(`📊 [${accountEmail}] Found ${backupFolders.length} backup folders`);

      if (backupFolders.length === 0) {
        console.log(`⚠️  [${accountEmail}] No backup folders found in archive ${archiveId}`);
        
        if (isTibxArchive) {
          console.log(`💡 [${accountEmail}] .tibx archives might require different access methods or be offline`);
        }
        
        return [];
      }

      const searchResults = await runParallelSearch(backupFolders, searchKeyword, cookies, archiveId, accountEmail);

      searchResults.forEach(result => {
        result.archiveId = archiveId;
        result.archiveName = archiveId.split('/').pop();
        result.datacenter = archiveId.split('//')[0];
        result.loginCredentials = {
          email: accountEmail,
          password: "***"
        };

        if (result.results && Array.isArray(result.results)) {
          result.results.forEach(fileResult => {
            fileResult.searchMetadata = {
              email: accountEmail,
              archiveId: archiveId,
              archiveName: archiveId.split('/').pop(),
              datacenter: archiveId.split('//')[0],
              searchTimestamp: new Date().toISOString()
            };
          });
        }
      });

      const totalResults = searchResults.reduce((sum, backup) => sum + backup.results.length, 0);
      console.log(`✅ [${accountEmail}] Archive search completed: ${totalResults} results`);

      // Call progress callback if provided to save results immediately
      if (progressCallback && searchResults.length > 0) {
        try {
          console.log(`📊 Calling progress callback with ${searchResults.length} backup results`);
          await progressCallback(accountEmail, searchResults);
        } catch (callbackError) {
          console.error(`⚠️  Progress callback failed: ${callbackError.message}`);
        }
      }

      return searchResults;

    } catch (error) {
      console.error(`❌ [${accountEmail}] Archive search failed:`, error.message);
      
      if (archiveId.includes('.tibx')) {
        console.log(`💡 [${accountEmail}] .tibx archive troubleshooting:`);
        console.log(`   - This archive format might be offline or require different access`);
        console.log(`   - Check if the archive is available in the Acronis web interface`);
        console.log(`   - Account might need different permissions for .tibx archives`);
      }
      
      return [];
    }
  }

  module.exports = {
    searchInArchive,
    runParallelSearch,
    getBackupFolders,
    generateRequestId
  };

} else {
  const { searchWorker } = require('./parallel_search.js');
  
  searchWorker(workerData.backup, workerData.searchKeyword, workerData.authCookies, workerData.archiveId, workerData.accountEmail)
    .then(result => {
      parentPort.postMessage({ success: true, data: result });
    })
    .catch(error => {
      parentPort.postMessage({ success: false, error: error.message });
    });
}
