const axios = require('axios');
const { wrapper } = require('axios-cookiejar-support');
const { <PERSON><PERSON><PERSON><PERSON> } = require('tough-cookie');

function generateRequestId() {
  return 'f' + Math.random().toString(16).substring(2, 17);
}

async function findVolumesRecursively(client, archiveId, currentPath = "", maxDepth = 3, currentDepth = 0) {
  if (currentDepth >= maxDepth) {
    return [];
  }

  try {
    const response = await client.get("https://cloud-wr-us2.acronis.com/ui/content", {
      headers: {
        "Accept": "application/json, text/plain, */*",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
        "Referer": "https://cloud-wr-us2.acronis.com/",
        "UI-REQUEST-ID": generateRequestId(),
      },
      params: {
        archiveId: archiveId,
        path: currentPath
      }
    });

    if (!response.data || !response.data.data) {
      return [];
    }

    const items = response.data.data;
    const volumes = [];

    // Debug: Log all items found in the backup folder
    console.log(`🐛 DEBUG: Found ${items.length} items in ${currentPath}:`);
    items.forEach((item, index) => {
      console.log(`   ${index + 1}. "${item.name}" (Type: ${item.itemType}, Folder: ${item.folderItem}, ID: ${item.id})`);
    });

    const driveItems = items.filter(item =>
      item.itemType === "FixedDrive" ||
      item.itemType === "RemovableDrive" ||
      (item.folderItem && item.name && item.name.match(/^[A-Z]:\s*\(.*\)$/))
    );

    console.log(`🐛 DEBUG: Filtered drive items: ${driveItems.length}`);
    driveItems.forEach((item, index) => {
      console.log(`   Drive ${index + 1}: "${item.name}" (Type: ${item.itemType})`);
    });

    if (driveItems.length > 0) {
      console.log(`🔍 Found ${driveItems.length} drive volumes at path "${currentPath}": ${driveItems.map(v => v.name).join(', ')}`);
    }
    volumes.push(...driveItems);

    // Also check for MountPoint items which might be the actual volumes we need
    const mountPointItems = items.filter(item =>
      item.itemType === "MountPoint" && item.folderItem
    );

    console.log(`🐛 DEBUG: Found MountPoint items: ${mountPointItems.length}`);
    mountPointItems.forEach((item, index) => {
      console.log(`   MountPoint ${index + 1}: "${item.name}" (ID: ${item.id})`);
    });

    if (mountPointItems.length > 0) {
      console.log(`🔍 Found ${mountPointItems.length} mount point volumes at path "${currentPath}": ${mountPointItems.map(v => v.name).join(', ')}`);
      volumes.push(...mountPointItems);
    }

    if (volumes.length === 0) {
      const folders = items.filter(item => item.folderItem && !item.name.includes("."));

      for (const folder of folders.slice(0, 3)) {
        const subPath = currentPath ? `${currentPath}/${folder.name}` : folder.name;
        try {
          const subVolumes = await findVolumesRecursively(client, archiveId, subPath, maxDepth, currentDepth + 1);
          volumes.push(...subVolumes);
        } catch (error) {
          console.log(`⚠️  Error exploring path ${subPath}: ${error.message}`);
        }
      }
    }

    return volumes;
  } catch (error) {
    console.log(`⚠️  Error exploring path ${currentPath}: ${error.message}`);
    return [];
  }
}

async function searchWorker(backup, searchKeyword, authCookies, archiveId, accountEmail = "unknown") {
  try {
    console.log(`🐛 DEBUG: Starting worker for backup ${backup.name}`);

    const jar = new CookieJar();
    authCookies.forEach(cookie => {
      jar.setCookieSync(`${cookie.name}=${cookie.value}`, "https://cloud-wr-us2.acronis.com");
    });

    console.log(`🐛 DEBUG: Restoring ${authCookies.length} cookies for ${backup.name}`);

    const client = wrapper(axios.create({
      jar,
      withCredentials: true,
      timeout: 900000
    }));

    console.log(`🐛 DEBUG: Client created for ${backup.name} with 15min timeout`);

    console.log(`🔍 Exploring backup structure: ${backup.name}`);
    const volumes = await findVolumesRecursively(client, archiveId, backup.name);

    if (volumes.length === 0) {
      console.log(`⚠️  No volumes found in ${backup.name}, trying direct search API fallback...`);
      
      try {
        console.log(`🔍 Attempting direct search in backup: ${backup.name}`);
        console.log(`   📋 Archive ID: ${archiveId}`);
        console.log(`   📁 Backup Path: ${backup.id || backup.name}`);
        console.log(`   🔎 Search Text: ${searchKeyword}`);

        const requestId = generateRequestId();
        console.log(`🐛 DEBUG: Making direct search request with ID: ${requestId}`);

        // Use real volume path for better search results
        const searchPath = backup.id || backup.name;
        const searchParams = {
          archiveId: archiveId,
          volumePath: searchPath,  // Use the actual backup/volume ID
          path: searchPath,
          searchText: searchKeyword,
          limit: 500000,  // Try to get more results
          offset: 0
        };

        console.log(`🐛 DEBUG: Direct search params:`, JSON.stringify(searchParams, null, 2));

        const searchResponse = await client.get("https://cloud-wr-us2.acronis.com/ui/search", {
          headers: {
            "Accept": "application/json, text/plain, */*",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
            "Referer": "https://cloud-wr-us2.acronis.com/",
            "UI-REQUEST-ID": requestId,
          },
          params: searchParams
        });

        console.log(`🐛 DEBUG: Direct search response received for ${backup.name}, status: ${searchResponse.status}`);

        if (searchResponse.status === 200 && searchResponse.data && searchResponse.data.data) {
          const searchResults = searchResponse.data.data;
          console.log(`✅ Direct search found ${searchResults.length} results in ${backup.name}`);
          
          const processedResults = searchResults.map(item => ({
            name: item.name,
            displayName: item.displayName || item.name,
            itemType: item.itemType,
            id: item.id,
            backupFolder: backup.name,
            archiveName: archiveId.split('/').pop(),
            accountEmail: accountEmail
          }));

          return {
            backupName: backup.name,
            results: processedResults,
            error: null,
            message: `Direct search found ${processedResults.length} results`
          };
        } else {
          console.log(`⚠️  Direct search API returned no results for ${backup.name}`);
        }
      } catch (directSearchError) {
        console.log(`❌ Direct search failed for ${backup.name}: ${directSearchError.message}`);
      }
      
      return {
        backupName: backup.name,
        results: [],
        error: null,
        message: "No volumes found in backup structure and direct search failed"
      };
    }

    console.log(`📁 Found ${volumes.length} volumes in ${backup.name}:`);
    volumes.forEach((v, index) => {
      console.log(`   ${index + 1}. ${v.name} (ID: ${v.id})`);
    });

    let allResults = [];

    for (const volume of volumes) {
      try {
        console.log(`🔍 Searching in volume: ${volume.name}`);
        console.log(`   📋 Archive ID: ${archiveId}`);
        console.log(`   📁 Volume Path: ${volume.id}`);
        console.log(`   🔎 Search Text: ${searchKeyword}`);

        const requestId = generateRequestId();
        console.log(`🐛 DEBUG: Making search request with ID: ${requestId}`);

        const searchParams = {
          archiveId: archiveId,
          volumePath: volume.id,
          path: volume.id,
          searchText: searchKeyword,
          limit: 500000,  // Try to get more results
          offset: 0
        };

        console.log(`🐛 DEBUG: Search params:`, JSON.stringify(searchParams, null, 2));

        const searchResponse = await client.get("https://cloud-wr-us2.acronis.com/ui/search", {
          headers: {
            "Accept": "application/json, text/plain, */*",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
            "Referer": "https://cloud-wr-us2.acronis.com/",
            "UI-REQUEST-ID": requestId,
          },
          params: searchParams
        });

        console.log(`🐛 DEBUG: Search response received for ${volume.name}, status: ${searchResponse.status}`);

        if (searchResponse.status !== 200) {
          console.log(`⚠️  Search API returned status ${searchResponse.status} for volume ${volume.name}`);
          continue;
        }

        if (!searchResponse.data || !searchResponse.data.data) {
          console.log(`⚠️  No search data returned for volume ${volume.name}`);
          continue;
        }

        const searchResults = searchResponse.data.data;
        console.log(`📊 Found ${searchResults.length} results in volume ${volume.name}`);

        if (searchResults.length > 0) {
          const processedResults = searchResults.map(item => ({
            name: item.name,
            displayName: item.displayName || item.name,
            itemType: item.itemType,
            id: item.id,
            backupFolder: backup.name,
            archiveName: archiveId.split('/').pop(),
            accountEmail: accountEmail
          }));

          allResults.push(...processedResults);
          console.log(`✅ Added ${processedResults.length} results from volume ${volume.name}`);
        }

      } catch (volumeError) {
        console.log(`❌ Error searching volume ${volume.name}: ${volumeError.message}`);
        continue;
      }
    }

    const message = allResults.length > 0 
      ? `Found ${allResults.length} results in ${volumes.length} volumes`
      : `No results found in ${volumes.length} volumes`;

    return {
      backupName: backup.name,
      results: allResults,
      error: null,
      message: message
    };

  } catch (error) {
    console.log(`❌ Worker error for ${backup.name}: ${error.message}`);
    return {
      backupName: backup.name,
      results: [],
      error: error.message,
      message: `Error: ${error.message}`
    };
  }
}

module.exports = {
  searchWorker,
  findVolumesRecursively,
  generateRequestId
};
