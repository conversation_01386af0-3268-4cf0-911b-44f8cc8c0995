const axios = require('axios');
const { wrapper } = require('axios-cookiejar-support');
const { <PERSON><PERSON><PERSON><PERSON> } = require('tough-cookie');

function generateRequestId() {
  return 'f' + Math.random().toString(16).substring(2, 17);
}

async function findVolumesRecursively(client, archiveId, currentPath = "", maxDepth = 2, currentDepth = 0) {
  if (currentDepth >= maxDepth) {
    return [];
  }

  try {
    const response = await client.get("https://cloud-wr-us2.acronis.com/ui/content", {
      headers: {
        "Accept": "application/json, text/plain, */*",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
        "Referer": "https://cloud-wr-us2.acronis.com/",
        "UI-REQUEST-ID": generateRequestId(),
      },
      params: {
        archiveId: archiveId,
        path: currentPath
      }
    });

    if (!response.data || !response.data.data) {
      return [];
    }

    const items = response.data.data;
    const volumes = [];

    // Debug: Log all items found in the backup folder
    console.log(`🐛 DEBUG: Found ${items.length} items in ${currentPath}:`);
    items.forEach((item, index) => {
      console.log(`   ${index + 1}. "${item.name}" (Type: ${item.itemType}, Folder: ${item.folderItem}, ID: ${item.id})`);
    });

    const driveItems = items.filter(item =>
      item.itemType === "FixedDrive" ||
      item.itemType === "RemovableDrive" ||
      (item.folderItem && item.name && item.name.match(/^[A-Z]:\s*\(.*\)$/))
    );

    console.log(`🐛 DEBUG: Filtered drive items: ${driveItems.length}`);
    driveItems.forEach((item, index) => {
      console.log(`   Drive ${index + 1}: "${item.name}" (Type: ${item.itemType})`);
    });

    if (driveItems.length > 0) {
      console.log(`🔍 Found ${driveItems.length} drive volumes at path "${currentPath}": ${driveItems.map(v => v.name).join(', ')}`);
    }
    volumes.push(...driveItems);

    // Also check for MountPoint items which might be the actual volumes we need
    const mountPointItems = items.filter(item =>
      item.itemType === "MountPoint" && item.folderItem
    );

    console.log(`🐛 DEBUG: Found MountPoint items: ${mountPointItems.length}`);
    mountPointItems.forEach((item, index) => {
      console.log(`   MountPoint ${index + 1}: "${item.name}" (ID: ${item.id})`);
    });

    if (mountPointItems.length > 0) {
      console.log(`🔍 Found ${mountPointItems.length} mount point volumes at path "${currentPath}": ${mountPointItems.map(v => v.name).join(', ')}`);
      volumes.push(...mountPointItems);
    }

    // If no volumes found yet, try looking for any folder items that might be volumes
    if (volumes.length === 0) {
      const folderItems = items.filter(item => item.folderItem);
      console.log(`🐛 DEBUG: No volumes found, checking ${folderItems.length} folder items as potential volumes`);
      folderItems.forEach((item, index) => {
        console.log(`   Folder ${index + 1}: "${item.name}" (Type: ${item.itemType}, ID: ${item.id})`);
      });

      // Add folder items as potential volumes if they look like drives
      const potentialVolumes = folderItems.filter(item =>
        item.name && (
          item.name.match(/^[A-Z]:\s*\(.*\)$/) ||  // Drive pattern like "C: (Windows)"
          item.name.match(/^[A-Z]+_[A-Z]+$/) ||    // System pattern like "SYSTEM_DRV"
          item.name.includes('(') && item.name.includes(')')  // Any parentheses pattern
        )
      );

      if (potentialVolumes.length > 0) {
        console.log(`🔍 Found ${potentialVolumes.length} potential volume folders: ${potentialVolumes.map(v => v.name).join(', ')}`);
        volumes.push(...potentialVolumes);
      }
    }

    // Only explore subdirectories if we haven't found volumes yet and we're at the root level
    if (volumes.length === 0 && currentDepth === 0) {
      const folders = items.filter(item => item.folderItem && !item.name.includes("."));
      console.log(`🔍 No volumes found at root, exploring ${folders.length} folders for volumes`);

      // Only explore a few key folders to find volumes
      for (const folder of folders.slice(0, 3)) {
        const subPath = currentPath ? `${currentPath}/${folder.name}` : folder.name;
        try {
          const subVolumes = await findVolumesRecursively(client, archiveId, subPath, maxDepth, currentDepth + 1);
          volumes.push(...subVolumes);
        } catch (error) {
          console.log(`⚠️  Error exploring path ${subPath}: ${error.message}`);
        }
      }
    }

    return volumes;
  } catch (error) {
    console.log(`⚠️  Error exploring path ${currentPath}: ${error.message}`);
    return [];
  }
}

async function searchWorker(backup, searchKeyword, authCookies, archiveId, accountEmail = "unknown") {
  try {
    console.log(`🐛 DEBUG: Starting worker for backup ${backup.name}`);

    const jar = new CookieJar();
    authCookies.forEach(cookie => {
      jar.setCookieSync(`${cookie.name}=${cookie.value}`, "https://cloud-wr-us2.acronis.com");
    });

    console.log(`🐛 DEBUG: Restoring ${authCookies.length} cookies for ${backup.name}`);

    const client = wrapper(axios.create({
      jar,
      withCredentials: true
      // No timeout - wait for actual API response (200, 500, etc.)
    }));

    console.log(`🐛 DEBUG: Client created for ${backup.name} with no timeout - waiting for API response`);

    console.log(`🔍 Exploring backup structure: ${backup.name}`);
    const volumes = await findVolumesRecursively(client, archiveId, backup.name);

    if (volumes.length === 0) {
      console.log(`⚠️  No volumes found in ${backup.name}, using API searcher fallback...`);

      try {
        console.log(`🔍 Using API searcher for backup: ${backup.name}`);
        console.log(`   📋 Archive ID: ${archiveId}`);
        console.log(`   🔎 Search Text: ${searchKeyword}`);

        const requestId = generateRequestId();
        console.log(`🐛 DEBUG: Making API search request with ID: ${requestId}`);

        // Use API searcher with minimal parameters
        const searchParams = {
          archiveId: archiveId,
          searchText: searchKeyword,
          limit: 500000,
          offset: 0
        };

        console.log(`🐛 DEBUG: API searcher params:`, JSON.stringify(searchParams, null, 2));

        const searchResponse = await client.get("https://cloud-wr-us2.acronis.com/ui/search", {
          headers: {
            "Accept": "application/json, text/plain, */*",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
            "Referer": "https://cloud-wr-us2.acronis.com/",
            "UI-REQUEST-ID": requestId,
          },
          params: searchParams
        });

        console.log(`🐛 DEBUG: API searcher response received, status: ${searchResponse.status}`);

        if (searchResponse.status === 200 && searchResponse.data && searchResponse.data.data) {
          const searchResults = searchResponse.data.data;
          console.log(`✅ API searcher found ${searchResults.length} results in ${backup.name}`);

          const processedResults = searchResults.map(item => ({
            name: item.name,
            displayName: item.displayName || item.name,
            itemType: item.itemType,
            id: item.id,
            backupFolder: backup.name,
            archiveName: archiveId.split('/').pop(),
            accountEmail: accountEmail
          }));

          return {
            backupName: backup.name,
            results: processedResults,
            error: null,
            message: `API searcher found ${processedResults.length} results`
          };
        } else {
          console.log(`⚠️  API searcher returned no results for ${backup.name}`);
        }
      } catch (apiSearchError) {
        console.log(`❌ API searcher failed for ${backup.name}: ${apiSearchError.message}`);
      }
      
      return {
        backupName: backup.name,
        results: [],
        error: null,
        message: "No volumes found and API searcher failed"
      };
    }

    console.log(`📁 Found ${volumes.length} volumes in ${backup.name}:`);
    volumes.forEach((v, index) => {
      console.log(`   ${index + 1}. ${v.name} (ID: ${v.id})`);
    });

    let allResults = [];

    // Search each volume with delays to prevent socket hang ups
    for (let i = 0; i < volumes.length; i++) {
      const volume = volumes[i];

      // Add delay between requests to prevent server overload
      if (i > 0) {
        console.log(`⏳ Waiting 2 seconds before next volume search...`);
        await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
      }

      try {
        console.log(`🔍 Searching in volume: ${volume.name} (${i + 1}/${volumes.length})`);
        console.log(`   📋 Archive ID: ${archiveId}`);
        console.log(`   📁 Volume Path: ${volume.id}`);
        console.log(`   🔎 Search Text: ${searchKeyword}`);

        const requestId = generateRequestId();
        console.log(`🐛 DEBUG: Making search request with ID: ${requestId}`);

        const searchParams = {
          archiveId: archiveId,
          volumePath: volume.id,
          path: volume.id,
          searchText: searchKeyword,
          limit: 500000,  // Try to get more results
          offset: 0
        };

        console.log(`🐛 DEBUG: Search params:`, JSON.stringify(searchParams, null, 2));

        const searchResponse = await client.get("https://cloud-wr-us2.acronis.com/ui/search", {
          headers: {
            "Accept": "application/json, text/plain, */*",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
            "Referer": "https://cloud-wr-us2.acronis.com/",
            "UI-REQUEST-ID": requestId,
          },
          params: searchParams
        });

        console.log(`🐛 DEBUG: Search response received for ${volume.name}, status: ${searchResponse.status}`);

        if (searchResponse.status !== 200) {
          console.log(`⚠️  Search API returned status ${searchResponse.status} for volume ${volume.name}`);
          continue;
        }

        if (!searchResponse.data || !searchResponse.data.data) {
          console.log(`⚠️  No search data returned for volume ${volume.name}`);
          continue;
        }

        const searchResults = searchResponse.data.data;
        console.log(`📊 Found ${searchResults.length} results in volume ${volume.name}`);

        if (searchResults.length > 0) {
          const processedResults = searchResults.map(item => ({
            name: item.name,
            displayName: item.displayName || item.name,
            itemType: item.itemType,
            id: item.id,
            backupFolder: backup.name,
            archiveName: archiveId.split('/').pop(),
            accountEmail: accountEmail
          }));

          allResults.push(...processedResults);
          console.log(`✅ Added ${processedResults.length} results from volume ${volume.name}`);
        }

      } catch (volumeError) {
        console.log(`❌ Error searching volume ${volume.name}: ${volumeError.message}`);
        continue;
      }
    }

    const message = allResults.length > 0 
      ? `Found ${allResults.length} results in ${volumes.length} volumes`
      : `No results found in ${volumes.length} volumes`;

    return {
      backupName: backup.name,
      results: allResults,
      error: null,
      message: message
    };

  } catch (error) {
    console.log(`❌ Worker error for ${backup.name}: ${error.message}`);
    return {
      backupName: backup.name,
      results: [],
      error: error.message,
      message: `Error: ${error.message}`
    };
  }
}

module.exports = {
  searchWorker,
  findVolumesRecursively,
  generateRequestId
};
