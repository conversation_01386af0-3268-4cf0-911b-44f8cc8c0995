const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const axios = require("axios");
const { CookieJar } = require("tough-cookie");
const { wrapper } = require("axios-cookiejar-support");
const { 
  generateRequestId, 
  saveSearchResults, 
  generateDashboard,
  searchWorker 
} = require('./parallel_search');

// Credentials are now loaded from accounts.txt via multi-account system
// Use: node multi_account_search.js for multi-account processing
const MAX_WORKERS = 5; // Parallel processing with 5 workers

// Archive ID will be discovered automatically for each account

// Worker thread code
if (!isMainThread) {
  (async () => {
    try {
      const result = await searchWorker(workerData);
      parentPort.postMessage({ success: true, data: result });
    } catch (error) {
      parentPort.postMessage({ success: false, error: error.message });
    }
  })();
} else {
  // Main thread code
  async function authenticateAndGetSession(email, password) {
    const jar = new <PERSON>ieJar();
    const client = wrapper(axios.create({
      jar,
      withCredentials: true,
      timeout: 900000, // 15 minutes timeout
      validateStatus: function (status) {
        return status >= 200 && status < 300; // Accept only 2xx status codes
      }
    }));

    try {
      console.log("🔐 Authenticating...");
      
      // Step 1: Login
      const loginResponse = await client.post("https://us4-cloud.acronis.com/api/1/login", {
        username: email,
        password: password
      }, {
        headers: {
          "Content-Type": "application/json",
          "X-Acronis-Api": "1",
          "X-Requested-With": "XMLHttpRequest",
          "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
          "Origin": "https://us4-cloud.acronis.com",
          "Referer": "https://us4-cloud.acronis.com/login",
          "Accept": "application/json, text/plain, */*",
          "DNT": "1"
        }
      });

      if (loginResponse.status !== 200 || !loginResponse.data.mfa_status) {
        throw new Error("Login failed: " + JSON.stringify(loginResponse.data));
      }

      console.log("✅ Login successful");

      // Step 2: Navigate to UI to establish session
      await client.get("https://us4-cloud.acronis.com/ui/", {
        headers: {
          "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
          "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
          "DNT": "1"
        }
      });

      // Step 3: Get webrestore link
      const webrestoreResponse = await client.get("https://us4-cloud.acronis.com/bc/api/ams/links/webrestore", {
        headers: {
          "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
          "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
          "Referer": "https://us4-cloud.acronis.com/ui/",
          "DNT": "1"
        },
        maxRedirects: 0,
        validateStatus: function (status) {
          return status >= 200 && status < 400;
        }
      });

      if (webrestoreResponse.status !== 302) {
        throw new Error("Failed to get webrestore redirect");
      }

      const redirectUrl = webrestoreResponse.headers.location;

      // Step 4: Follow JWT redirect
      await client.get(redirectUrl, {
        headers: {
          "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
          "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
          "DNT": "1"
        }
      });

      console.log("🎯 Session established");

      // Extract cookies for workers
      const cookies = [];
      const cookieStrings = jar.getCookiesSync("https://cloud-wr-us2.acronis.com");
      for (const cookie of cookieStrings) {
        cookies.push({
          cookie: cookie.toString(),
          url: "https://cloud-wr-us2.acronis.com"
        });
      }

      return { client, cookies };
      
    } catch (error) {
      console.error("❌ Authentication failed:", error.message);
      throw error;
    }
  }

  async function discoverAllArchives(client) {
    console.log("� Discovering archive ID for account...");

    try {
      // Get list of available archives using the boxes endpoint
      const boxesResponse = await client.get("https://cloud-wr-us2.acronis.com/ui/boxes", {
        headers: {
          "Accept": "application/json, text/plain, */*",
          "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
          "Referer": "https://cloud-wr-us2.acronis.com/",
          "UI-REQUEST-ID": generateRequestId(),
          "DNT": "1"
        }
      });

      if (boxesResponse.data && Array.isArray(boxesResponse.data)) {
        console.log(`� Found ${boxesResponse.data.length} available archives`);

        // Log all available archives
        boxesResponse.data.forEach((archive, index) => {
          console.log(`📁 Archive ${index + 1}: ${archive.id} (${archive.displayName})`);
        });

        // Return all archives for comprehensive searching
        if (boxesResponse.data.length > 0) {
          console.log(`✅ Will search in all ${boxesResponse.data.length} archives`);
          return boxesResponse.data.map(archive => archive.id);
        }
      }

      // Fallback: Create archive ID based on email pattern
      const emailUsername = email.split('@')[0];
      const fallbackArchiveId = `113//99DBC6FF-1353-4699-820F-3A372B5AC282/${emailUsername}`;
      console.log(`🔧 Using fallback archive ID: ${fallbackArchiveId}`);
      return [fallbackArchiveId];

    } catch (error) {
      console.log(`❌ Error discovering archives: ${error.message}`);
      // Create archive ID based on email pattern as fallback
      const emailUsername = email.split('@')[0];
      const fallbackArchiveId = `113//99DBC6FF-1353-4699-820F-3A372B5AC282/${emailUsername}`;
      console.log(`⚠️  Using email-based fallback archive ID: ${fallbackArchiveId}`);
      return [fallbackArchiveId];
    }
  }

  async function getBackupFolders(client, archiveId) {
    console.log("📁 Discovering backup folders...");

    try {
      const backupsResponse = await client.get("https://cloud-wr-us2.acronis.com/ui/content", {
        headers: {
          "Accept": "application/json, text/plain, */*",
          "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
          "Referer": "https://cloud-wr-us2.acronis.com/",
          "UI-REQUEST-ID": generateRequestId(),
        },
        params: {
          archiveId: archiveId,
          path: ""
        }
      });

      if (!backupsResponse.data || !backupsResponse.data.data) {
        console.log(`⚠️  No data returned for archive ${archiveId}`);
        return [];
      }

      // Handle different archive structures
      let backupFolders = backupsResponse.data.data.filter(item =>
        item.folderItem && item.name.startsWith("Backup #")
      );

      // Sort backup folders numerically (Backup #1, #2, #3, ..., #10, #11, etc.)
      if (backupFolders.length > 0) {
        backupFolders.sort((a, b) => {
          const na = parseInt(a.name.replace('Backup #', ''), 10);
          const nb = parseInt(b.name.replace('Backup #', ''), 10);
          return na - nb;
        });
        console.log(`📊 Found ${backupFolders.length} backup folders (sorted numerically)`);
      }

      // If no "Backup #" folders found, look for drive-based structure (FixedDrive items)
      if (backupFolders.length === 0) {
        const driveItems = backupsResponse.data.data.filter(item =>
          item.itemType === "FixedDrive" || (item.folderItem && item.name.match(/^[A-Z0-9-]+:?$/))
        );

        if (driveItems.length > 0) {
          console.log(`📊 Found ${driveItems.length} drive-based backup items (no traditional backup folders)`);
          // Treat each drive as a "backup folder" for search purposes
          backupFolders = driveItems.map(drive => ({
            ...drive,
            name: drive.name || drive.displayName || `Drive-${drive.id}`,
            // Mark as drive-based for different handling in search
            isDriveBased: true
          }));
        }
      }

      console.log(`📊 Found ${backupFolders.length} backup folders/drives`);
      return backupFolders;

    } catch (error) {
      console.log(`❌ Error accessing archive ${archiveId}: ${error.message}`);

      if (error.response && error.response.status === 404) {
        console.log(`🔍 Archive appears to be inaccessible (404). Details:`);
        console.log(`   - Archive ID: ${archiveId}`);
        console.log(`   - This might be a .tibx archive or offline archive`);
        console.log(`   - Account might not have access permissions`);
      }

      // Return empty array instead of throwing to allow other archives to be processed
      return [];
    }
  }

  async function runParallelSearch(backupFolders, searchKeyword, authCookies, archiveId) {
    console.log(`🚀 Starting parallel search with ${Math.min(MAX_WORKERS, backupFolders.length)} workers...`);

    const results = [];
    const workers = [];

    // Process backups in batches to limit concurrent workers
    for (let i = 0; i < backupFolders.length; i += MAX_WORKERS) {
      const batch = backupFolders.slice(i, i + MAX_WORKERS);
      const batchPromises = [];

      for (const backup of batch) {
        const promise = new Promise((resolve, reject) => {
          const worker = new Worker(__filename, {
            workerData: {
              backup,
              searchKeyword,
              authCookies,
              archiveId
            }
          });

          worker.on('message', (message) => {
            if (message.success) {
              console.log(`✅ ${backup.name}: ${message.data.message}`);
              resolve(message.data);
            } else {
              console.log(`❌ ${backup.name}: ${message.error}`);
              resolve({
                backupName: backup.name,
                results: [],
                error: message.error,
                message: `Error: ${message.error}`
              });
            }
            worker.terminate();
          });

          worker.on('error', (error) => {
            console.log(`❌ ${backup.name}: Worker error - ${error.message}`);
            resolve({
              backupName: backup.name,
              results: [],
              error: error.message,
              message: `Worker error: ${error.message}`
            });
          });

          workers.push(worker);
        });

        batchPromises.push(promise);
      }

      // Wait for current batch to complete
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // Small delay between batches to prevent overwhelming the server
      if (i + MAX_WORKERS < backupFolders.length) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    return results;
  }

  async function appendToResultsFile(newSearchData, isProgressUpdate = false) {
    const fs = require('fs').promises;
    const path = require('path');

    const resultsDir = path.join(__dirname, 'search_results');
    const resultFile = path.join(resultsDir, 'RESULT.json');

    try {
      // Ensure directory exists
      await fs.mkdir(resultsDir, { recursive: true });

      let existingData = { searches: [] };

      // Try to read existing file
      try {
        const existingContent = await fs.readFile(resultFile, 'utf8');
        existingData = JSON.parse(existingContent);
        if (!existingData.searches) {
          existingData.searches = [];
        }
      } catch (error) {
        // File doesn't exist or is invalid, start fresh
        console.log("📝 Creating new RESULT.json file");
      }

      if (isProgressUpdate) {
        // For progress updates, replace the last search if it has the same keyword and timestamp (same search session)
        const lastSearch = existingData.searches[existingData.searches.length - 1];
        if (lastSearch &&
            lastSearch.keyword === newSearchData.keyword &&
            Math.abs(new Date(lastSearch.timestamp) - new Date(newSearchData.timestamp)) < 60000) { // Within 1 minute
          // Replace the last search with updated progress
          existingData.searches[existingData.searches.length - 1] = newSearchData;
        } else {
          // Add as new search
          existingData.searches.push(newSearchData);
        }
      } else {
        // Final update - add new search
        existingData.searches.push(newSearchData);
      }

      // Update summary statistics
      existingData.totalSearches = existingData.searches.length;
      existingData.totalResults = existingData.searches.reduce((sum, search) => sum + search.totalResults, 0);
      existingData.lastUpdated = new Date().toISOString();

      // Save updated file
      await fs.writeFile(resultFile, JSON.stringify(existingData, null, 2));

      if (isProgressUpdate) {
        console.log(`📁 Progress updated in RESULT.json`);
      } else {
        console.log(`📁 Results appended to RESULT.json`);
        console.log(`📊 Total searches: ${existingData.totalSearches}`);
        console.log(`📄 Total results: ${existingData.totalResults}`);
      }

    } catch (error) {
      console.error("❌ Error saving to RESULT.json:", error.message);
    }
  }

  async function main(email = null, password = null) {
    if (!email || !password) {
      console.log("🔍 Acronis Search Engine");
      console.log("=================================");
      console.log("❌ No credentials provided!");
      console.log("");
      console.log("For multi-account search, use:");
      console.log("   node multi_account_search.js");
      console.log("");
      console.log("For single account search, provide credentials:");
      console.log("   const { main } = require('./fast_search.js');");
      console.log("   main('<EMAIL>', 'password');");
      return;
    }

    const startTime = Date.now();
    console.log("🔍 Starting Acronis Parallel Search Engine");
    console.log("=" .repeat(50));
    console.log(`📧 Account: ${email}`);
    
    try {
      // Authentication
      const { client, cookies } = await authenticateAndGetSession(email, password);

      // Discover all archives for this account
      const archiveIds = await discoverAllArchives(client);

      // Search configuration
      const searchKeyword = "microsoft";
      console.log(`🎯 Searching for: "${searchKeyword}"`);

      let allSearchResults = [];
      let totalBackupsSearched = 0;

      // Search in each archive
      for (let i = 0; i < archiveIds.length; i++) {
        const archiveId = archiveIds[i];
        console.log(`\n📦 Processing archive ${i + 1}/${archiveIds.length}: ${archiveId}`);

        try {
          // Get backup folders for this archive
          const backupFolders = await getBackupFolders(client, archiveId);

          if (backupFolders.length === 0) {
            console.log(`⚠️  No backup folders found in archive: ${archiveId}`);
            continue;
          }

          totalBackupsSearched += backupFolders.length;

          // Run parallel search for this archive
          const archiveSearchResults = await runParallelSearch(backupFolders, searchKeyword, cookies, archiveId);

          // Add archive info and login credentials to each result
          archiveSearchResults.forEach(result => {
            result.archiveId = archiveId;
            result.archiveName = archiveId.split('/').pop(); // Extract machine name
            result.datacenter = archiveId.split('//')[0];
            result.loginCredentials = {
              email: email,
              password: password
            };

            // Also add login info to each individual file result
            if (result.results && Array.isArray(result.results)) {
              result.results.forEach(fileResult => {
                fileResult.searchMetadata = {
                  email: email,
                  password: password,
                  archiveId: archiveId,
                  archiveName: archiveId.split('/').pop(),
                  datacenter: archiveId.split('//')[0],
                  searchTimestamp: new Date().toISOString()
                };
              });
            }
          });

          allSearchResults.push(...archiveSearchResults);

          // Save progress after each archive completes
          const currentTime = Date.now();
          const currentDuration = Math.round((currentTime - startTime) / 1000);
          const currentTotalResults = allSearchResults.reduce((sum, backup) => sum + backup.results.length, 0);

          const progressData = {
            keyword: searchKeyword,
            timestamp: new Date().toISOString(),
            searchDuration: currentDuration,
            totalResults: currentTotalResults,
            backupsSearched: totalBackupsSearched,
            backupsWithResults: allSearchResults.filter(backup => backup.results.length > 0).length,
            archiveIds: archiveIds,
            archiveCount: archiveIds.length,
            parallel: true,
            workerCount: MAX_WORKERS,
            status: `In Progress - Archive ${i + 1}/${archiveIds.length} completed`,
            loginCredentials: {
              email: email,
              password: password
            },
            archiveInfo: archiveIds.map(archiveId => ({
              archiveId: archiveId,
              machineName: archiveId.split('/').pop(),
              datacenter: archiveId.split('//')[0]
            })),
            results: allSearchResults
          };

          // Update RESULT.json with current progress
          await appendToResultsFile(progressData, true);
          console.log(`📊 Progress saved: ${currentTotalResults} results so far`);

        } catch (archiveError) {
          console.log(`❌ Error processing archive ${archiveId}: ${archiveError.message}`);
          continue;
        }
      }

      if (allSearchResults.length === 0) {
        console.log("❌ No results found in any archive");
        return;
      }
      
      // Calculate statistics
      const endTime = Date.now();
      const searchDuration = Math.round((endTime - startTime) / 1000);
      const totalResults = allSearchResults.reduce((sum, backup) => sum + backup.results.length, 0);
      const backupsWithResults = allSearchResults.filter(backup => backup.results.length > 0).length;

      // Prepare data for saving with login credentials and archive info
      const searchData = {
        keyword: searchKeyword,
        timestamp: new Date().toISOString(),
        searchDuration,
        totalResults,
        backupsSearched: totalBackupsSearched,
        backupsWithResults,
        archiveIds: archiveIds,
        archiveCount: archiveIds.length,
        parallel: true, // Using parallel processing with workers
        workerCount: MAX_WORKERS,
        // Login credentials for tracking
        loginCredentials: {
          email: email,
          password: password
        },
        // Archive information for reference
        archiveInfo: archiveIds.map(archiveId => ({
          archiveId: archiveId,
          machineName: archiveId.split('/').pop(),
          datacenter: archiveId.split('//')[0]
        })),
        results: allSearchResults
      };
      
      // Save results and generate dashboard
      console.log("\n" + "=" .repeat(50));
      console.log("📊 SEARCH COMPLETED");
      console.log("=" .repeat(50));
      console.log(`⏱️  Duration: ${searchDuration} seconds`);
      console.log(`� Archives searched: ${archiveIds.length}`);
      console.log(`�📁 Total backups searched: ${totalBackupsSearched}`);
      console.log(`✅ Backups with results: ${backupsWithResults}`);
      console.log(`📄 Total results: ${totalResults}`);
      console.log(`⚡ Workers used: ${MAX_WORKERS}`);

      // Save to RESULT.json (final update)
      await appendToResultsFile(searchData, false);

      console.log("\n🎉 Search completed successfully!");
      console.log(`📁 Results appended to: search_results/RESULT.json`);
      console.log(`🔐 Login credentials included: ${email}`);
      console.log(`📦 Archive IDs included: ${archiveIds.length} archives`);
      console.log(`🌐 Open dashboard: search_results/flexible_dashboard.html`);
      
    } catch (error) {
      console.error("❌ Search failed:", error.message);
      process.exit(1);
    }
  }

  // Export function for multi-account search
  async function searchInArchive(client, cookies, archiveId, searchKeyword, accountEmail, timeout = 900000, progressCallback = null) {
    try {
      console.log(`🔍 [${accountEmail}] Searching archive ${archiveId} for "${searchKeyword}"`);

      // Check if this is a .tibx archive (different format)
      const isTibxArchive = archiveId.includes('.tibx');
      if (isTibxArchive) {
        console.log(`📦 [${accountEmail}] Detected .tibx archive format`);
      }

      // Discover backup folders
      const backupFolders = await getBackupFolders(client, archiveId);
      console.log(`📊 [${accountEmail}] Found ${backupFolders.length} backup folders`);

      if (backupFolders.length === 0) {
        console.log(`⚠️  [${accountEmail}] No backup folders found in archive ${archiveId}`);

        if (isTibxArchive) {
          console.log(`💡 [${accountEmail}] .tibx archives might require different access methods or be offline`);
        }

        return [];
      }

      // Run parallel search (use original function for now)
      const searchResults = await runParallelSearch(backupFolders, searchKeyword, cookies, archiveId);

      // Add account info to results
      searchResults.forEach(result => {
        result.archiveId = archiveId;
        result.archiveName = archiveId.split('/').pop();
        result.datacenter = archiveId.split('//')[0];
        result.loginCredentials = {
          email: accountEmail,
          password: "***" // Don't expose password in results
        };

        if (result.results && Array.isArray(result.results)) {
          result.results.forEach(fileResult => {
            fileResult.searchMetadata = {
              email: accountEmail,
              archiveId: archiveId,
              archiveName: archiveId.split('/').pop(),
              datacenter: archiveId.split('//')[0],
              searchTimestamp: new Date().toISOString()
            };
          });
        }
      });

      const totalResults = searchResults.reduce((sum, backup) => sum + backup.results.length, 0);
      console.log(`✅ [${accountEmail}] Archive search completed: ${totalResults} results`);

      return searchResults;

    } catch (error) {
      console.error(`❌ [${accountEmail}] Archive search failed:`, error.message);

      // Provide specific guidance for .tibx archives
      if (archiveId.includes('.tibx')) {
        console.log(`💡 [${accountEmail}] .tibx archive troubleshooting:`);
        console.log(`   - This archive format might be offline or require different access`);
        console.log(`   - Check if the archive is available in the Acronis web interface`);
        console.log(`   - Account might need different permissions for .tibx archives`);
      }

      return [];
    }
  }

  // Enhanced parallel search with progress updates
  async function runParallelSearchWithProgress(backupFolders, searchKeyword, cookies, archiveId, progressCallback, accountEmail) {
    const maxWorkers = 5;
    const results = [];

    console.log(`🚀 Starting parallel search with ${maxWorkers} workers...`);

    // Process backups in batches
    for (let i = 0; i < backupFolders.length; i += maxWorkers) {
      const batch = backupFolders.slice(i, i + maxWorkers);

      const batchPromises = batch.map(async (backupFolder) => {
        try {
          const result = await searchWorker(backupFolder, searchKeyword, cookies, archiveId);

          // Call progress callback after each backup completes
          if (progressCallback && result && result.results && result.results.length > 0) {
            await progressCallback(accountEmail, [result]);
          }

          return result;
        } catch (error) {
          console.error(`❌ Error in worker for ${backupFolder.name}:`, error.message);
          return { backupName: backupFolder.name, results: [], error: error.message };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults.filter(r => r !== null));

      // Small delay between batches to prevent overwhelming the server
      if (i + maxWorkers < backupFolders.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return results;
  }

  // Run the main function
  if (require.main === module) {
    main();
  }

  // Export functions for multi-account search
  module.exports = {
    main,
    searchInArchive,
    getBackupFolders,
    runParallelSearch,
    runParallelSearchWithProgress
  };
}
