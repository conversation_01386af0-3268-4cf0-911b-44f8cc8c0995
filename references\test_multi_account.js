const { loadConfig, loadAccounts, logDebug } = require('./multi_account_search.js');

async function testMultiAccountSystem() {
    console.log('🧪 Testing Multi-Account Search System');
    console.log('=====================================');
    
    try {
        // Test configuration loading
        console.log('\n1. Testing configuration loading...');
        const config = await loadConfig();
        console.log('✅ Configuration loaded successfully');
        console.log(`   - Keywords: ${config.searchSettings.keywords.join(', ')}`);
        console.log(`   - Max concurrent accounts: ${config.concurrencySettings.maxConcurrentAccounts}`);
        console.log(`   - Workers per account: ${config.concurrencySettings.workersPerAccount}`);
        
        // Test account loading
        console.log('\n2. Testing account loading...');
        const accounts = await loadAccounts();
        console.log(`✅ Loaded ${accounts.length} accounts`);
        accounts.forEach((account, index) => {
            console.log(`   ${index + 1}. ${account.email}`);
        });
        
        // Test debug logging
        console.log('\n3. Testing debug logging...');
        logDebug('Test debug message', 'INFO');
        logDebug('Test error message', 'ERROR', '<EMAIL>');
        logDebug('Test success message', 'SUCCESS', '<EMAIL>');
        console.log('✅ Debug logging working');
        
        console.log('\n🎉 All tests passed! Multi-account system is ready.');
        console.log('\nTo run the full multi-account search:');
        console.log('   node multi_account_search.js');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

if (require.main === module) {
    testMultiAccountSystem();
}
