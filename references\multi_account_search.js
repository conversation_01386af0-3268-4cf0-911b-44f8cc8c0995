const fs = require('fs').promises;
const path = require('path');
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');

// Configuration and state management
let config = {};
let debugLogs = [];
let allAccounts = [];
let accountResults = new Map();
let globalResults = new Map(); // Track results per account
let startTime = Date.now();

// Load configuration
async function loadConfig() {
    try {
        const configData = await fs.readFile('config.json', 'utf8');
        config = JSON.parse(configData);
        console.log('✅ Configuration loaded successfully');
        return config;
    } catch (error) {
        console.error('❌ Failed to load config.json:', error.message);
        // Use default configuration
        config = {
            searchSettings: { keywords: ["microsoft"], searchTimeout: 900000 },
            concurrencySettings: { maxConcurrentAccounts: 20, workersPerAccount: 5 },
            outputSettings: { resultFile: "search_results/RESULT.json", debugLogFile: "DEBUG_LOGS.txt" }
        };
        return config;
    }
}

// Load accounts from accounts.txt
async function loadAccounts() {
    try {
        const accountsData = await fs.readFile('accounts.txt', 'utf8');
        const lines = accountsData.split('\n');
        
        allAccounts = lines
            .map(line => line.trim())
            .filter(line => line && !line.startsWith('#'))
            .map(line => {
                const [email, password] = line.split(':');
                if (!email || !password) {
                    logDebug(`Invalid account format: ${line}`, 'ERROR');
                    return null;
                }
                return { email: email.trim(), password: password.trim() };
            })
            .filter(account => account !== null);

        console.log(`📋 Loaded ${allAccounts.length} accounts from accounts.txt`);
        logDebug(`Loaded ${allAccounts.length} accounts for processing`, 'INFO');
        return allAccounts;
    } catch (error) {
        console.error('❌ Failed to load accounts.txt:', error.message);
        logDebug(`Failed to load accounts.txt: ${error.message}`, 'ERROR');
        return [];
    }
}

// Debug logging function
function logDebug(message, level = 'INFO', accountEmail = null) {
    const timestamp = new Date().toISOString();
    const logEntry = {
        timestamp,
        level,
        account: accountEmail,
        message
    };
    
    debugLogs.push(logEntry);
    
    const logLine = `[${timestamp}] [${level}] ${accountEmail ? `[${accountEmail}] ` : ''}${message}`;
    console.log(logLine);
}

// Save debug logs to file
async function saveDebugLogs() {
    try {
        const logContent = debugLogs.map(log => 
            `[${log.timestamp}] [${log.level}] ${log.account ? `[${log.account}] ` : ''}${log.message}`
        ).join('\n');
        
        await fs.writeFile(config.outputSettings.debugLogFile, logContent);
        console.log(`📝 Debug logs saved to ${config.outputSettings.debugLogFile}`);
    } catch (error) {
        console.error('❌ Failed to save debug logs:', error.message);
    }
}

// Account worker function
async function processAccount(account, keywords) {
    const { email, password } = account;
    
    try {
        logDebug(`Starting authentication for account`, 'INFO', email);
        
        // Import the search functionality
        const { searchWorker } = require('./parallel_search.js');
        
        // Authenticate account
        const authResult = await authenticateAccount(email, password);
        if (!authResult.success) {
            logDebug(`Authentication failed: ${authResult.error}`, 'ERROR', email);
            return {
                account: email,
                success: false,
                error: authResult.error,
                results: []
            };
        }
        
        logDebug(`Authentication successful`, 'SUCCESS', email);
        
        // Discover archives
        const archives = await discoverArchives(authResult, email);
        if (archives.length === 0) {
            logDebug(`No archives found for account`, 'WARNING', email);
            return {
                account: email,
                success: true,
                archives: 0,
                results: []
            };
        }
        
        logDebug(`Found ${archives.length} archives`, 'INFO', email);
        
        // Search all archives
        const searchResults = [];
        for (const archiveId of archives) {
            try {
                logDebug(`Starting search in archive: ${archiveId}`, 'INFO', email);
                const archiveResults = await searchArchive(authResult, archiveId, keywords, email);
                searchResults.push(...archiveResults);
                logDebug(`Archive search completed: ${archiveResults.length} results`, 'SUCCESS', email);

                // Update RESULT.json immediately after each archive
                await updateProgressiveResults(email, searchResults, archives.length);

            } catch (error) {
                logDebug(`Archive search failed for ${archiveId}: ${error.message}`, 'ERROR', email);
            }
        }
        
        logDebug(`Account processing completed: ${searchResults.length} total results`, 'SUCCESS', email);
        
        return {
            account: email,
            success: true,
            archives: archives.length,
            results: searchResults
        };
        
    } catch (error) {
        logDebug(`Account processing failed: ${error.message}`, 'ERROR', email);
        return {
            account: email,
            success: false,
            error: error.message,
            results: []
        };
    }
}

// Authenticate account using existing authentication logic
async function authenticateAccount(email, password) {
    try {
        const axios = require('axios');
        const wrapper = require('axios-cookiejar-support').wrapper;
        const { CookieJar } = require('tough-cookie');

        const jar = new CookieJar();
        const client = wrapper(axios.create({
            jar,
            withCredentials: true,
            timeout: config.authenticationSettings.loginTimeout || 30000
        }));

        // Step 1: Login using working endpoint
        const loginResponse = await client.post("https://us4-cloud.acronis.com/api/1/login", {
            username: email,
            password: password
        }, {
            headers: {
                "Content-Type": "application/json",
                "X-Acronis-Api": "1",
                "X-Requested-With": "XMLHttpRequest",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
                "Origin": "https://us4-cloud.acronis.com",
                "Referer": "https://us4-cloud.acronis.com/login",
                "Accept": "application/json, text/plain, */*",
                "DNT": "1"
            }
        });

        if (loginResponse.status !== 200) {
            throw new Error("Login failed: " + JSON.stringify(loginResponse.data));
        }

        logDebug(`Login successful`, 'SUCCESS', email);

        // Step 2: Navigate to UI to establish session
        await client.get("https://us4-cloud.acronis.com/ui/", {
            headers: {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "DNT": "1"
            }
        });

        // Step 3: Get webrestore link
        logDebug(`Getting webrestore link...`, 'INFO', email);
        const webrestoreResponse = await client.get("https://us4-cloud.acronis.com/bc/api/ams/links/webrestore", {
            headers: {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "Referer": "https://us4-cloud.acronis.com/ui/",
                "DNT": "1"
            },
            maxRedirects: 0,
            validateStatus: function (status) {
                return status >= 200 && status < 400;
            },
            timeout: config.authenticationSettings.loginTimeout || 120000
        });

        if (webrestoreResponse.status !== 302) {
            throw new Error("Failed to get webrestore redirect");
        }

        const redirectUrl = webrestoreResponse.headers.location;
        logDebug(`Got webrestore redirect URL`, 'INFO', email);

        // Step 4: Follow JWT redirect
        logDebug(`Following JWT redirect...`, 'INFO', email);
        await client.get(redirectUrl, {
            headers: {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "DNT": "1"
            },
            timeout: config.authenticationSettings.loginTimeout || 120000
        });

        // Extract cookies for workers
        const cookies = [];
        const cookieStrings = jar.getCookiesSync("https://cloud-wr-us2.acronis.com");
        for (const cookie of cookieStrings) {
            cookies.push({
                cookie: cookie.toString(),
                url: "https://cloud-wr-us2.acronis.com"
            });
        }

        logDebug(`Authentication successful - ${cookies.length} cookies obtained`, 'SUCCESS', email);

        return {
            success: true,
            cookies: cookies,
            jar: jar,
            client: client
        };

    } catch (error) {
        logDebug(`Authentication failed: ${error.message}`, 'ERROR', email);
        return {
            success: false,
            error: error.message
        };
    }
}

// Discover archives for account
async function discoverArchives(authData, email) {
    try {
        const { generateRequestId } = require('./parallel_search.js');

        const response = await authData.client.get("https://cloud-wr-us2.acronis.com/ui/boxes", {
            headers: {
                "Accept": "application/json, text/plain, */*",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
                "Referer": "https://cloud-wr-us2.acronis.com/",
                "UI-REQUEST-ID": generateRequestId(),
                "DNT": "1"
            }
        });

        if (!response.data || !Array.isArray(response.data)) {
            throw new Error('Invalid response from boxes endpoint');
        }

        const archiveIds = response.data.map(box => box.id).filter(id => id);
        logDebug(`Discovered ${archiveIds.length} archives`, 'INFO', email);

        // Log all available archives
        response.data.forEach((archive, index) => {
            logDebug(`Archive ${index + 1}: ${archive.id} (${archive.displayName})`, 'INFO', email);
        });

        return archiveIds;
    } catch (error) {
        logDebug(`Archive discovery failed: ${error.message}`, 'ERROR', email);

        // Fallback: Create archive ID based on email pattern
        const emailUsername = email.split('@')[0];
        const fallbackArchiveId = `113//99DBC6FF-1353-4699-820F-3A372B5AC282/${emailUsername}`;
        logDebug(`Using fallback archive ID: ${fallbackArchiveId}`, 'WARNING', email);
        return [fallbackArchiveId];
    }
}

// Search archive using existing search logic
async function searchArchive(authData, archiveId, keywords, email) {
    try {
        logDebug(`Starting search in archive ${archiveId}`, 'INFO', email);

        // Import the existing search functionality
        const { searchInArchive } = require('./fast_search.js');

        const results = [];

        // Progress callback for real-time updates
        const progressCallback = async (accountEmail, partialResults) => {
            try {
                // Get current results for this account
                const currentAccountResults = globalResults.get(accountEmail) || { results: [] };
                const updatedResults = [...(currentAccountResults.results || []), ...partialResults];

                // Update progressive results
                await updateProgressiveResults(accountEmail, updatedResults, 1);
            } catch (error) {
                logDebug(`Progress callback error: ${error.message}`, 'ERROR', accountEmail);
            }
        };

        for (const keyword of keywords) {
            logDebug(`Searching for keyword: ${keyword}`, 'INFO', email);

            try {
                const keywordResults = await searchInArchive(
                    authData.client,
                    authData.cookies,
                    archiveId,
                    keyword,
                    email,
                    config.searchSettings.searchTimeout,
                    progressCallback
                );

                // Tag results with account information
                keywordResults.forEach(result => {
                    if (result.results && Array.isArray(result.results)) {
                        result.results.forEach(fileResult => {
                            fileResult.searchMetadata = {
                                ...fileResult.searchMetadata,
                                email: email,
                                archiveId: archiveId,
                                keyword: keyword,
                                searchTimestamp: new Date().toISOString()
                            };
                        });
                    }
                    result.accountEmail = email;
                    result.searchKeyword = keyword;
                });

                results.push(...keywordResults);

            } catch (searchError) {
                logDebug(`Keyword search failed for "${keyword}": ${searchError.message}`, 'ERROR', email);

                // For 404 errors, provide more specific information
                if (searchError.message.includes('404')) {
                    logDebug(`Archive ${archiveId} appears to be inaccessible (404). This could be due to:`, 'WARNING', email);
                    logDebug(`- Archive is offline or moved`, 'WARNING', email);
                    logDebug(`- Different archive format (.tibx) requiring special handling`, 'WARNING', email);
                    logDebug(`- Account permissions or subscription level`, 'WARNING', email);
                }

                // Continue with other keywords even if one fails
                continue;
            }
        }

        logDebug(`Archive search completed: ${results.length} backup results`, 'SUCCESS', email);
        return results;

    } catch (error) {
        logDebug(`Archive search failed: ${error.message}`, 'ERROR', email);

        // Provide specific guidance for common issues
        if (error.message.includes('404')) {
            logDebug(`Archive access denied (404). Possible solutions:`, 'INFO', email);
            logDebug(`1. Check if archive is online and accessible`, 'INFO', email);
            logDebug(`2. Verify account has proper permissions`, 'INFO', email);
            logDebug(`3. Archive might use different format (.tibx) requiring alternative access method`, 'INFO', email);
        }

        return [];
    }
}

// Process accounts in batches
async function processAccountsBatch(accounts, keywords) {
    const maxConcurrent = config.concurrencySettings.maxConcurrentAccounts;
    const results = [];
    
    for (let i = 0; i < accounts.length; i += maxConcurrent) {
        const batch = accounts.slice(i, i + maxConcurrent);
        logDebug(`Processing batch ${Math.floor(i/maxConcurrent) + 1}: ${batch.length} accounts`);
        
        const batchPromises = batch.map(account => processAccount(account, keywords));
        const batchResults = await Promise.all(batchPromises);
        
        results.push(...batchResults);
        
        // Save progress after each batch
        await saveProgressResults(results);
        
        // Delay between batches
        if (i + maxConcurrent < accounts.length) {
            await new Promise(resolve => setTimeout(resolve, config.concurrencySettings.delayBetweenBatches));
        }
    }
    
    return results;
}

// Update progressive results for real-time updates
async function updateProgressiveResults(accountEmail, newResults, totalArchives) {
    try {
        // Update the global results map for this account
        globalResults.set(accountEmail, {
            account: accountEmail,
            success: true,
            archives: totalArchives,
            results: newResults,
            lastUpdate: new Date().toISOString()
        });

        // Get all current results from all accounts
        const allCurrentResults = Array.from(globalResults.values());
        const flatResults = allCurrentResults.flatMap(r => r.results || []);

        // Calculate total results count
        // flatResults contains backup objects, each with a results array of file results
        const totalResultCount = flatResults.reduce((sum, backup) => {
            if (backup && backup.results && Array.isArray(backup.results)) {
                return sum + backup.results.length;
            }
            return sum;
        }, 0);

        const searchData = {
            timestamp: new Date().toISOString(),
            searchDuration: Math.round((Date.now() - startTime) / 1000),
            totalAccounts: allAccounts.length,
            processedAccounts: allCurrentResults.length,
            successfulAccounts: allCurrentResults.filter(r => r.success).length,
            failedAccounts: allCurrentResults.filter(r => !r.success).length,
            totalResults: totalResultCount,
            keywords: config.searchSettings.keywords,
            multiAccount: true,
            progressiveUpdate: true,
            accounts: allCurrentResults.map(r => ({
                email: r.account,
                success: r.success,
                archives: r.archives || 0,
                results: r.results ? r.results.reduce((sum, backup) => {
                    if (backup && backup.results && Array.isArray(backup.results)) {
                        return sum + backup.results.length;
                    }
                    return sum;
                }, 0) : 0,
                error: r.error || null,
                lastUpdate: r.lastUpdate
            })),
            results: flatResults
        };

        // Ensure directory exists
        await fs.mkdir(path.dirname(config.outputSettings.resultFile), { recursive: true });

        // Atomic write to prevent JSON corruption during dashboard reads
        const tempFile = config.outputSettings.resultFile + '.tmp';
        await fs.writeFile(tempFile, JSON.stringify(searchData, null, 2), 'utf8');
        await fs.rename(tempFile, config.outputSettings.resultFile);

        // Calculate results from this specific account
        const accountResultCount = newResults.reduce((sum, backup) => {
            if (backup && backup.results && Array.isArray(backup.results)) {
                return sum + backup.results.length;
            }
            return sum;
        }, 0);

        console.log(`📊 Progressive update: ${totalResultCount} total results (${accountResultCount} from ${accountEmail})`);
        logDebug(`Progressive update saved: ${totalResultCount} total results`, 'INFO', accountEmail);

    } catch (error) {
        logDebug(`Failed to save progressive results: ${error.message}`, 'ERROR', accountEmail);
    }
}

// Save progress results
async function saveProgressResults(results) {
    try {
        const searchData = {
            timestamp: new Date().toISOString(),
            searchDuration: Math.round((Date.now() - startTime) / 1000),
            totalAccounts: allAccounts.length,
            processedAccounts: results.length,
            successfulAccounts: results.filter(r => r.success).length,
            failedAccounts: results.filter(r => !r.success).length,
            totalResults: results.reduce((sum, r) => {
                if (r.success && r.results && Array.isArray(r.results)) {
                    return sum + r.results.reduce((backupSum, backup) => {
                        if (backup && backup.results && Array.isArray(backup.results)) {
                            return backupSum + backup.results.length;
                        }
                        return backupSum;
                    }, 0);
                }
                return sum;
            }, 0),
            keywords: config.searchSettings.keywords,
            multiAccount: true,
            accounts: results.map(r => ({
                email: r.account,
                success: r.success,
                archives: r.archives || 0,
                results: r.success && r.results ? r.results.reduce((sum, backup) => {
                    if (backup && backup.results && Array.isArray(backup.results)) {
                        return sum + backup.results.length;
                    }
                    return sum;
                }, 0) : 0,
                error: r.error || null
            })),
            results: results.filter(r => r.success).flatMap(r => r.results || [])
        };

        // Ensure directory exists
        await fs.mkdir(path.dirname(config.outputSettings.resultFile), { recursive: true });

        // Save results
        await fs.writeFile(config.outputSettings.resultFile, JSON.stringify(searchData, null, 2));

        console.log(`📊 Progress saved: ${searchData.totalResults} results from ${searchData.processedAccounts} accounts`);

    } catch (error) {
        logDebug(`Failed to save progress: ${error.message}`, 'ERROR');
    }
}

// Main execution function
async function main() {
    console.log('🚀 Starting Multi-Account Acronis Search Engine');
    console.log('==================================================');
    
    startTime = Date.now();
    
    // Load configuration
    await loadConfig();
    
    // Load accounts
    const accounts = await loadAccounts();
    if (accounts.length === 0) {
        console.log('❌ No valid accounts found. Please check accounts.txt');
        return;
    }
    
    // Start processing
    logDebug(`Starting multi-account search with ${accounts.length} accounts`);
    logDebug(`Keywords: ${config.searchSettings.keywords.join(', ')}`);
    logDebug(`Max concurrent accounts: ${config.concurrencySettings.maxConcurrentAccounts}`);
    
    const results = await processAccountsBatch(accounts, config.searchSettings.keywords);
    
    // Final results summary
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    const totalResults = successful.reduce((sum, r) => {
        if (r.results && Array.isArray(r.results)) {
            return sum + r.results.reduce((backupSum, backup) => {
                if (backup && backup.results && Array.isArray(backup.results)) {
                    return backupSum + backup.results.length;
                }
                return backupSum;
            }, 0);
        }
        return sum;
    }, 0);
    
    console.log('\n🎉 Multi-Account Search Completed!');
    console.log('==================================');
    console.log(`📊 Total Accounts: ${accounts.length}`);
    console.log(`✅ Successful: ${successful.length}`);
    console.log(`❌ Failed: ${failed.length}`);
    console.log(`📄 Total Results: ${totalResults}`);
    console.log(`⏱️  Duration: ${Math.round((Date.now() - startTime) / 1000)}s`);
    
    // Save final debug logs
    await saveDebugLogs();
    
    console.log(`\n📁 Results saved to: ${config.outputSettings.resultFile}`);
    console.log(`📝 Debug logs saved to: ${config.outputSettings.debugLogFile}`);
    console.log(`🌐 View dashboard: http://localhost:8080`);
}

// Export for use as module
module.exports = {
    main,
    loadConfig,
    loadAccounts,
    processAccount,
    logDebug
};

// Run if called directly
if (require.main === module) {
    main().catch(error => {
        console.error('💥 Fatal error:', error);
        process.exit(1);
    });
}
