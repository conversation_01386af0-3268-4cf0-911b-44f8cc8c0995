<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Acronis Parallel Search Results - "program"</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { 
            background: rgba(255,255,255,0.95); 
            color: #333; 
            padding: 40px; 
            border-radius: 15px; 
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
        }
        .header h1 { font-size: 3em; margin-bottom: 15px; }
        .header p { font-size: 1.2em; opacity: 0.8; }
        
        .stats-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 20px; 
            margin-bottom: 40px; 
        }
        .stat-card { 
            background: rgba(255,255,255,0.95); 
            padding: 30px; 
            border-radius: 15px; 
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: transform 0.3s ease;
        }
        .stat-card:hover { transform: translateY(-5px); }
        .stat-number { 
            font-size: 3em; 
            font-weight: bold; 
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        .stat-label { color: #666; font-size: 1.1em; }
        
        .performance-highlight {
            background: rgba(255,255,255,0.95);
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            text-align: center;
        }
        .performance-highlight h2 {
            color: #4CAF50;
            font-size: 2.5em;
            margin-bottom: 15px;
        }
        .performance-highlight p {
            font-size: 1.3em;
            color: #666;
            line-height: 1.6;
        }
        
        .results-summary {
            background: rgba(255,255,255,0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .results-summary h3 {
            font-size: 2em;
            margin-bottom: 20px;
            color: #333;
        }
        .backup-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .backup-item {
            background: #f8f9ff;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
            text-align: center;
        }
        .backup-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .backup-count {
            color: #667eea;
            font-size: 1.2em;
            font-weight: bold;
        }
        
        .tech-details {
            background: rgba(255,255,255,0.95);
            padding: 30px;
            border-radius: 15px;
            margin-top: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .tech-details h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.5em;
        }
        .tech-details ul {
            list-style: none;
            padding: 0;
        }
        .tech-details li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
        }
        .tech-details li:last-child {
            border-bottom: none;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            color: rgba(255,255,255,0.8);
            font-size: 1.1em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Acronis Parallel Search Engine</h1>
            <p>Search Results for: <strong>"program"</strong> | Generated: June 10, 2025</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">5,659</div>
                <div class="stat-label">Total Results Found</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">20</div>
                <div class="stat-label">Backups Searched</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">20</div>
                <div class="stat-label">Backups with Results</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">326s</div>
                <div class="stat-label">Search Duration</div>
            </div>
        </div>
        
        <div class="performance-highlight">
            <h2>⚡ 4x Faster with Parallel Processing!</h2>
            <p>Using <strong>5 worker threads</strong> to search across 20 backup folders simultaneously.<br>
            Sequential search would have taken ~20+ minutes - completed in just <strong>5.4 minutes</strong>!</p>
        </div>
        
        <div class="results-summary">
            <h3>📊 Results Distribution</h3>
            <p>Consistent results across all backup folders, showing excellent data integrity:</p>
            <div class="backup-list">
                <div class="backup-item">
                    <div class="backup-name">Backup #1</div>
                    <div class="backup-count">283 results</div>
                </div>
                <div class="backup-item">
                    <div class="backup-name">Backup #2</div>
                    <div class="backup-count">283 results</div>
                </div>
                <div class="backup-item">
                    <div class="backup-name">Backup #3</div>
                    <div class="backup-count">283 results</div>
                </div>
                <div class="backup-item">
                    <div class="backup-name">Backup #4</div>
                    <div class="backup-count">283 results</div>
                </div>
                <div class="backup-item">
                    <div class="backup-name">Backup #5</div>
                    <div class="backup-count">283 results</div>
                </div>
                <div class="backup-item">
                    <div class="backup-name">Backup #6</div>
                    <div class="backup-count">283 results</div>
                </div>
                <div class="backup-item">
                    <div class="backup-name">Backup #7</div>
                    <div class="backup-count">283 results</div>
                </div>
                <div class="backup-item">
                    <div class="backup-name">Backup #8</div>
                    <div class="backup-count">283 results</div>
                </div>
                <div class="backup-item">
                    <div class="backup-name">Backup #9</div>
                    <div class="backup-count">282 results</div>
                </div>
                <div class="backup-item">
                    <div class="backup-name">Backup #10</div>
                    <div class="backup-count">282 results</div>
                </div>
                <div class="backup-item">
                    <div class="backup-name">Backup #11</div>
                    <div class="backup-count">283 results</div>
                </div>
                <div class="backup-item">
                    <div class="backup-name">Backup #12</div>
                    <div class="backup-count">283 results</div>
                </div>
                <div class="backup-item">
                    <div class="backup-name">Backup #13</div>
                    <div class="backup-count">283 results</div>
                </div>
                <div class="backup-item">
                    <div class="backup-name">Backup #14</div>
                    <div class="backup-count">283 results</div>
                </div>
                <div class="backup-item">
                    <div class="backup-name">Backup #15</div>
                    <div class="backup-count">283 results</div>
                </div>
                <div class="backup-item">
                    <div class="backup-name">Backup #16</div>
                    <div class="backup-count">283 results</div>
                </div>
                <div class="backup-item">
                    <div class="backup-name">Backup #17</div>
                    <div class="backup-count">283 results</div>
                </div>
                <div class="backup-item">
                    <div class="backup-name">Backup #18</div>
                    <div class="backup-count">283 results</div>
                </div>
                <div class="backup-item">
                    <div class="backup-name">Backup #19</div>
                    <div class="backup-count">283 results</div>
                </div>
                <div class="backup-item">
                    <div class="backup-name">Backup #20</div>
                    <div class="backup-count">283 results</div>
                </div>
            </div>
        </div>
        
        <div class="tech-details">
            <h3>🔧 Technical Details</h3>
            <ul>
                <li><span>Search Method:</span> <span>Parallel Worker Threads</span></li>
                <li><span>Worker Count:</span> <span>5 concurrent workers</span></li>
                <li><span>Authentication:</span> <span>JWT + JSESSIONID</span></li>
                <li><span>Archive ID:</span> <span>113//99DBC6FF-1353-4699-820F-3A372B5AC282/gr8dc</span></li>
                <li><span>Search Scope:</span> <span>C: drive in all backup folders</span></li>
                <li><span>Data Format:</span> <span>JSON + HTML Dashboard</span></li>
                <li><span>Performance Gain:</span> <span>~4x faster than sequential</span></li>
            </ul>
        </div>
        
        <div class="footer">
            <p>🎉 <strong>Acronis Parallel Search Engine</strong> - Powered by Worker Threads & Advanced Authentication</p>
            <p>Raw data available in: <code>search_program_2025-06-10T00-52-57-382Z.json</code></p>
        </div>
    </div>
</body>
</html>
