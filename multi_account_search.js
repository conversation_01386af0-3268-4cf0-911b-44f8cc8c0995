const fs = require('fs').promises;
const axios = require('axios');
const { wrapper } = require('axios-cookiejar-support');
const { <PERSON><PERSON>J<PERSON> } = require('tough-cookie');

let config = {};
const globalResults = new Map();

async function loadConfig() {
    try {
        const configData = await fs.readFile('config.json', 'utf8');
        config = JSON.parse(configData);
        return config;
    } catch (error) {
        config = {
            searchSettings: { keywords: ["microsoft"], searchTimeout: 900000 },
            concurrencySettings: { maxConcurrentAccounts: 20, workersPerAccount: 5 }, // 5 workers with no timeout - wait for API response
            outputSettings: { resultFile: "search_results/RESULT.json", debugLogFile: "DEBUG_LOGS.txt" },
            authenticationSettings: { loginTimeout: 120000, retryAttempts: 3, retryDelay: 10000 }
        };
        return config;
    }
}

async function loadAccounts() {
    try {
        const accountsData = await fs.readFile('accounts.txt', 'utf8');
        return accountsData.trim().split('\n')
            .filter(line => line.trim() && !line.startsWith('#'))
            .map(line => {
                const [email, password] = line.split(':');
                return { email: email.trim(), password: password.trim() };
            });
    } catch (error) {
        console.error('❌ Failed to load accounts.txt:', error.message);
        return [];
    }
}

function logDebug(message, level = 'INFO', email = '') {
    const timestamp = new Date().toISOString();
    const prefix = email ? `[${email}]` : '';
    const logMessage = `[${timestamp}] [${level}] ${prefix} ${message}`;
    console.log(logMessage);
}

async function authenticateAccount(email, password) {
    try {
        logDebug(`Starting authentication for account`, 'INFO', email);

        const jar = new CookieJar();
        const client = wrapper(axios.create({
            jar,
            withCredentials: true,
            timeout: config.authenticationSettings.loginTimeout || 120000
        }));

        const loginResponse = await client.post("https://us4-cloud.acronis.com/api/1/login", {
            username: email,
            password: password
        }, {
            headers: {
                "Content-Type": "application/json",
                "X-Acronis-Api": "1",
                "X-Requested-With": "XMLHttpRequest",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
                "Origin": "https://us4-cloud.acronis.com",
                "Referer": "https://us4-cloud.acronis.com/login",
                "Accept": "application/json, text/plain, */*"
            }
        });

        if (loginResponse.status !== 200) {
            throw new Error("Login failed: " + JSON.stringify(loginResponse.data));
        }

        logDebug(`Login successful`, 'SUCCESS', email);

        await client.get("https://us4-cloud.acronis.com/ui/", {
            headers: {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"
            }
        });

        logDebug(`Getting webrestore link...`, 'INFO', email);
        const webrestoreResponse = await client.get("https://us4-cloud.acronis.com/bc/api/ams/links/webrestore", {
            headers: {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "Referer": "https://us4-cloud.acronis.com/ui/"
            },
            maxRedirects: 0,
            validateStatus: function (status) {
                return status >= 200 && status < 400;
            },
            timeout: config.authenticationSettings.loginTimeout || 120000
        });

        if (webrestoreResponse.status !== 302) {
            throw new Error("Failed to get webrestore redirect");
        }

        const redirectUrl = webrestoreResponse.headers.location;
        logDebug(`Got webrestore redirect URL`, 'INFO', email);

        logDebug(`Following JWT redirect...`, 'INFO', email);
        await client.get(redirectUrl, {
            headers: {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"
            },
            timeout: config.authenticationSettings.loginTimeout || 120000
        });

        const cookies = await jar.getCookies("https://cloud-wr-us2.acronis.com");
        logDebug(`Authentication successful - ${cookies.length} cookies obtained`, 'SUCCESS', email);

        return {
            client,
            cookies: cookies.map(cookie => ({
                name: cookie.key,
                value: cookie.value,
                domain: cookie.domain,
                path: cookie.path
            }))
        };

    } catch (error) {
        logDebug(`Authentication failed: ${error.message}`, 'ERROR', email);
        throw error;
    }
}

async function discoverArchives(authData, email) {
    try {
        const { generateRequestId } = require('./parallel_search.js');

        logDebug(`Making request to boxes endpoint`, 'INFO', email);
        const response = await authData.client.get("https://cloud-wr-us2.acronis.com/ui/boxes", {
            headers: {
                "Accept": "application/json, text/plain, */*",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
                "Referer": "https://cloud-wr-us2.acronis.com/",
                "UI-REQUEST-ID": generateRequestId()
            }
        });

        logDebug(`Boxes endpoint response status: ${response.status}`, 'INFO', email);
        logDebug(`Response data type: ${typeof response.data}, isArray: ${Array.isArray(response.data)}`, 'INFO', email);

        if (response.data) {
            logDebug(`Response data length: ${response.data.length}`, 'INFO', email);
            logDebug(`Raw response data: ${JSON.stringify(response.data)}`, 'INFO', email);
        }

        if (!response.data || !Array.isArray(response.data)) {
            throw new Error('Invalid response from boxes endpoint');
        }

        const archives = response.data.map(archive => ({
            id: archive.id,
            displayName: archive.displayName || archive.name || 'Unknown Archive'
        }));

        logDebug(`Discovered ${archives.length} archives`, 'INFO', email);
        archives.forEach((archive, index) => {
            logDebug(`Archive ${index + 1}: ${archive.id} (${archive.displayName})`, 'INFO', email);
        });

        return archives;

    } catch (error) {
        logDebug(`Failed to discover archives: ${error.message}`, 'ERROR', email);
        return [];
    }
}

async function searchArchive(authData, archiveId, keywords, email) {
    try {
        logDebug(`Starting search in archive ${archiveId}`, 'INFO', email);

        const { searchInArchiveOptimized } = require('./optimized_search.js');
        const results = [];

        const progressCallback = async (accountEmail, partialResults) => {
            try {
                // Get current account results or create new structure
                const currentAccountResults = globalResults.get(accountEmail) || {
                    account: accountEmail,
                    success: true,
                    archives: 0,
                    results: [],
                    error: null,
                    lastUpdate: new Date().toISOString()
                };

                // Add new results to existing results
                const updatedResults = [...(currentAccountResults.results || []), ...partialResults];
                currentAccountResults.results = updatedResults;
                currentAccountResults.lastUpdate = new Date().toISOString();

                // Update global results
                globalResults.set(accountEmail, currentAccountResults);

                // Save progressive results to file
                await updateProgressiveResults(accountEmail, updatedResults, 1);

                logDebug(`Progressive update: added ${partialResults.length} results, total: ${updatedResults.length}`, 'INFO', accountEmail);
            } catch (error) {
                logDebug(`Progress callback error: ${error.message}`, 'ERROR', accountEmail);
            }
        };

        for (const keyword of keywords) {
            logDebug(`Searching for keyword: ${keyword}`, 'INFO', email);

            try {
                const keywordResults = await searchInArchiveOptimized(
                    authData.client,
                    authData.cookies,
                    archiveId,
                    keyword,
                    email,
                    config.searchSettings.searchTimeout,
                    progressCallback
                );

                keywordResults.forEach(result => {
                    if (result.results && Array.isArray(result.results)) {
                        result.results.forEach(fileResult => {
                            fileResult.searchMetadata = {
                                ...fileResult.searchMetadata,
                                email: email,
                                archiveId: archiveId,
                                keyword: keyword,
                                searchTimestamp: new Date().toISOString()
                            };
                        });
                    }
                    result.accountEmail = email;
                    result.searchKeyword = keyword;
                });

                results.push(...keywordResults);
                
            } catch (searchError) {
                logDebug(`Keyword search failed for "${keyword}": ${searchError.message}`, 'ERROR', email);
                
                if (searchError.message.includes('404')) {
                    logDebug(`Archive ${archiveId} appears to be inaccessible (404)`, 'WARNING', email);
                }
                continue;
            }
        }

        logDebug(`Archive search completed: ${results.length} backup results`, 'SUCCESS', email);
        return results;

    } catch (error) {
        logDebug(`Archive search failed: ${error.message}`, 'ERROR', email);
        return [];
    }
}

async function updateProgressiveResults(accountEmail, newResults, archiveCount) {
    try {
        // Ensure search_results directory exists
        const path = require('path');
        const resultDir = path.dirname(config.outputSettings.resultFile);
        try {
            await fs.mkdir(resultDir, { recursive: true });
        } catch (dirError) {
            // Directory might already exist, ignore error
        }

        const allCurrentResults = Array.from(globalResults.values());
        const flatResults = allCurrentResults.flatMap(r => r.results || []);

        const totalResultCount = flatResults.reduce((sum, backup) => {
            if (backup && backup.results && Array.isArray(backup.results)) {
                return sum + backup.results.length;
            }
            return sum;
        }, 0);

        const accountResultCount = newResults.reduce((sum, backup) => {
            if (backup && backup.results && Array.isArray(backup.results)) {
                return sum + backup.results.length;
            }
            return sum;
        }, 0);

        console.log(`📊 Progressive update: ${totalResultCount} total results (${accountResultCount} from ${accountEmail})`);

        const progressData = {
            timestamp: new Date().toISOString(),
            searchDuration: Math.floor((Date.now() - startTime) / 1000),
            totalAccounts: globalResults.size,
            processedAccounts: allCurrentResults.filter(r => r.success).length,
            successfulAccounts: allCurrentResults.filter(r => r.success).length,
            failedAccounts: allCurrentResults.filter(r => !r.success).length,
            totalResults: totalResultCount,
            keywords: config.searchSettings.keywords,
            multiAccount: true,
            progressiveUpdate: true,
            accounts: allCurrentResults.map(r => ({
                email: r.account,
                success: r.success,
                archives: r.archives || 0,
                results: r.results ? r.results.reduce((sum, backup) => {
                    if (backup && backup.results && Array.isArray(backup.results)) {
                        return sum + backup.results.length;
                    }
                    return sum;
                }, 0) : 0,
                error: r.error || null,
                lastUpdate: r.lastUpdate
            })),
            results: flatResults
        };

        await fs.writeFile(config.outputSettings.resultFile, JSON.stringify(progressData, null, 2));
        logDebug(`Progressive update saved: ${totalResultCount} total results`, 'INFO', accountEmail);

    } catch (error) {
        logDebug(`Failed to save progressive results: ${error.message}`, 'ERROR', accountEmail);
    }
}

async function saveProgressResults(results) {
    try {
        // Ensure search_results directory exists
        const path = require('path');
        const resultDir = path.dirname(config.outputSettings.resultFile);
        try {
            await fs.mkdir(resultDir, { recursive: true });
        } catch (dirError) {
            // Directory might already exist, ignore error
        }

        const totalResults = results.reduce((sum, r) => {
            if (r.success && r.results && Array.isArray(r.results)) {
                return sum + r.results.reduce((backupSum, backup) => {
                    if (backup && backup.results && Array.isArray(backup.results)) {
                        return backupSum + backup.results.length;
                    }
                    return backupSum;
                }, 0);
            }
            return sum;
        }, 0);

        const progressData = {
            timestamp: new Date().toISOString(),
            searchDuration: Math.floor((Date.now() - startTime) / 1000),
            totalAccounts: results.length,
            processedAccounts: results.length, // All accounts were processed
            successfulAccounts: results.filter(r => r.success).length,
            failedAccounts: results.filter(r => !r.success).length,
            totalResults: totalResults,
            keywords: config.searchSettings.keywords,
            multiAccount: true,
            progressiveUpdate: false,
            accounts: results.map(r => ({
                email: r.account,
                success: r.success,
                archives: r.archives || 0,
                results: r.success && r.results ? r.results.reduce((sum, backup) => {
                    if (backup && backup.results && Array.isArray(backup.results)) {
                        return sum + backup.results.length;
                    }
                    return sum;
                }, 0) : 0,
                error: r.error || null,
                lastUpdate: r.lastUpdate || new Date().toISOString()
            })),
            results: results.filter(r => r.success).flatMap(r => r.results || [])
        };

        await fs.writeFile(config.outputSettings.resultFile, JSON.stringify(progressData, null, 2));
        console.log(`💾 Final results saved to ${config.outputSettings.resultFile}`);
        console.log(`📊 Summary: ${totalResults} total results from ${progressData.successfulAccounts} successful accounts`);

    } catch (error) {
        console.error('❌ Failed to save final results:', error.message);
        console.error('   File path:', config.outputSettings.resultFile);
        console.error('   Error details:', error);
    }
}

let startTime;

async function processAccount(account) {
    const result = {
        account: account.email,
        success: false,
        archives: 0,
        results: [],
        error: null,
        lastUpdate: new Date().toISOString()
    };

    // Retry authentication up to 3 times
    let authData = null;
    let lastError = null;

    for (let attempt = 1; attempt <= config.authenticationSettings.retryAttempts; attempt++) {
        try {
            logDebug(`Authentication attempt ${attempt}/${config.authenticationSettings.retryAttempts}`, 'INFO', account.email);
            authData = await authenticateAccount(account.email, account.password);
            logDebug(`Authentication successful on attempt ${attempt}`, 'SUCCESS', account.email);
            break;
        } catch (error) {
            lastError = error;
            logDebug(`Authentication attempt ${attempt} failed: ${error.message}`, 'WARNING', account.email);

            if (attempt < config.authenticationSettings.retryAttempts) {
                const delay = config.authenticationSettings.retryDelay || 10000;
                logDebug(`Waiting ${delay}ms before retry...`, 'INFO', account.email);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }

    if (!authData) {
        result.error = `Authentication failed after ${config.authenticationSettings.retryAttempts} attempts: ${lastError.message}`;
        return result;
    }

    try {

        const archives = await discoverArchives(authData, account.email);
        logDebug(`Found ${archives.length} archives`, 'INFO', account.email);
        result.archives = archives.length;

        if (archives.length === 0) {
            logDebug('No archives/backups found for this account', 'INFO', account.email);
            logDebug('This could mean: account has no backups, different backup service, or access restrictions', 'INFO', account.email);
            result.success = true; // This is a valid state, not an error
            globalResults.set(account.email, result);
            return result;
        }

        for (const archive of archives) {
            logDebug(`Starting search in archive: ${archive.id}`, 'INFO', account.email);
            const archiveResults = await searchArchive(authData, archive.id, config.searchSettings.keywords, account.email);
            result.results.push(...archiveResults);
            logDebug(`Archive search completed: ${archiveResults.length} results`, 'SUCCESS', account.email);
        }

        result.success = true;
        globalResults.set(account.email, result);

    } catch (error) {
        logDebug(`Account processing failed: ${error.message}`, 'ERROR', account.email);
        result.error = error.message;
    }

    return result;
}

async function main() {
    try {
        console.log('🚀 Starting Multi-Account Acronis Search Engine');
        console.log('==================================================');

        startTime = Date.now();
        await loadConfig();
        console.log('✅ Configuration loaded successfully');

        const accounts = await loadAccounts();
        console.log(`📋 Loaded ${accounts.length} accounts from accounts.txt`);

        if (accounts.length === 0) {
            console.log('❌ No accounts found. Please check accounts.txt');
            return;
        }

        logDebug(`Loaded ${accounts.length} accounts for processing`);
        logDebug(`Starting multi-account search with ${accounts.length} accounts`);
        logDebug(`Keywords: ${config.searchSettings.keywords.join(', ')}`);
        logDebug(`Max concurrent accounts: ${config.concurrencySettings.maxConcurrentAccounts}`);

        const results = [];
        const maxConcurrent = config.concurrencySettings.maxConcurrentAccounts || 20;

        for (let i = 0; i < accounts.length; i += maxConcurrent) {
            const batch = accounts.slice(i, i + maxConcurrent);
            logDebug(`Processing batch ${Math.floor(i / maxConcurrent) + 1}: ${batch.length} accounts`);

            const batchPromises = batch.map(account => processAccount(account));
            const batchResults = await Promise.all(batchPromises);
            results.push(...batchResults);

            if (i + maxConcurrent < accounts.length) {
                await new Promise(resolve => setTimeout(resolve, config.concurrencySettings.delayBetweenBatches || 3000));
            }
        }

        const successful = results.filter(r => r.success);
        const failed = results.filter(r => !r.success);

        const totalResults = successful.reduce((sum, r) => {
            if (r.results && Array.isArray(r.results)) {
                return sum + r.results.reduce((backupSum, backup) => {
                    if (backup && backup.results && Array.isArray(backup.results)) {
                        return backupSum + backup.results.length;
                    }
                    return backupSum;
                }, 0);
            }
            return sum;
        }, 0);

        const accountsWithArchives = successful.filter(r => r.archives > 0);
        const accountsWithoutArchives = successful.filter(r => r.archives === 0);
        const accountsWithResults = successful.filter(r => r.results && r.results.length > 0);

        console.log('\n📊 Search Summary:');
        console.log(`✅ Successful accounts: ${successful.length}`);
        console.log(`   📦 Accounts with archives: ${accountsWithArchives.length}`);
        console.log(`   📭 Accounts without archives: ${accountsWithoutArchives.length}`);
        console.log(`   🔍 Accounts with search results: ${accountsWithResults.length}`);
        console.log(`❌ Failed accounts: ${failed.length}`);
        console.log(`📁 Total results found: ${totalResults}`);

        await saveProgressResults(results);

        if (accountsWithoutArchives.length > 0) {
            console.log('\n📭 Accounts without archives/backups:');
            accountsWithoutArchives.forEach(r => console.log(`   ${r.account}: No archives found`));
        }

        if (failed.length > 0) {
            console.log('\n❌ Failed accounts:');
            failed.forEach(f => console.log(`   ${f.account}: ${f.error}`));
        }

        console.log('\n🎉 Multi-account search completed!');

    } catch (error) {
        console.error('❌ Fatal error:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    loadConfig,
    loadAccounts,
    logDebug,
    authenticateAccount,
    discoverArchives,
    searchArchive,
    processAccount,
    main
};
