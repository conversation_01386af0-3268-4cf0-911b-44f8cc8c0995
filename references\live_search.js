#!/usr/bin/env node

/**
 * Live Search Dashboard Launcher
 * 
 * This script provides a seamless experience by:
 * 1. Starting the dashboard server
 * 2. Opening the browser to the live dashboard
 * 3. Starting the multi-account search
 * 4. Providing real-time status updates
 */

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 Starting Live Search Dashboard...\n');

// Configuration
const DASHBOARD_PORT = 8080;
const DASHBOARD_URL = `http://localhost:${DASHBOARD_PORT}`;
const SEARCH_RESULTS_DIR = './search_results';

// Ensure search results directory exists
if (!fs.existsSync(SEARCH_RESULTS_DIR)) {
    fs.mkdirSync(SEARCH_RESULTS_DIR, { recursive: true });
}

// Step 1: Start Dashboard Server
console.log('📊 Step 1: Starting dashboard server...');
const dashboardServer = spawn('node', ['serve_dashboard.js'], {
    cwd: SEARCH_RESULTS_DIR,
    stdio: ['pipe', 'pipe', 'pipe']
});

dashboardServer.stdout.on('data', (data) => {
    console.log(`📊 Dashboard: ${data.toString().trim()}`);
});

dashboardServer.stderr.on('data', (data) => {
    console.error(`📊 Dashboard Error: ${data.toString().trim()}`);
});

// Step 2: Wait for server to start, then open browser
setTimeout(() => {
    console.log('🌐 Step 2: Opening live dashboard in browser...');
    console.log(`🔗 Dashboard URL: ${DASHBOARD_URL}`);
    
    // Open browser (cross-platform)
    const openCommand = process.platform === 'win32' ? 'start' : 
                       process.platform === 'darwin' ? 'open' : 'xdg-open';
    
    exec(`${openCommand} ${DASHBOARD_URL}`, (error) => {
        if (error) {
            console.log(`⚠️  Could not auto-open browser. Please manually open: ${DASHBOARD_URL}`);
        } else {
            console.log('✅ Browser opened successfully');
        }
    });
}, 2000);

// Step 3: Start Multi-Account Search
setTimeout(() => {
    console.log('\n🔍 Step 3: Starting multi-account search for "program"...');
    console.log('📱 Watch the dashboard for live updates!\n');
    
    const searchProcess = spawn('node', ['multi_account_search.js'], {
        stdio: ['pipe', 'pipe', 'pipe']
    });
    
    searchProcess.stdout.on('data', (data) => {
        const output = data.toString().trim();
        if (output) {
            console.log(`🔍 Search: ${output}`);
        }
    });
    
    searchProcess.stderr.on('data', (data) => {
        const error = data.toString().trim();
        if (error) {
            console.error(`🔍 Search Error: ${error}`);
        }
    });
    
    searchProcess.on('close', (code) => {
        console.log(`\n✅ Search completed with code: ${code}`);
        console.log('🎉 Check the dashboard for final results!');
        console.log(`🔗 Dashboard: ${DASHBOARD_URL}`);
    });
    
}, 3000);

// Handle cleanup on exit
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down live search dashboard...');
    dashboardServer.kill();
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down live search dashboard...');
    dashboardServer.kill();
    process.exit(0);
});

// Keep the process alive
console.log('\n📋 Live Search Dashboard Status:');
console.log('   📊 Dashboard Server: Starting...');
console.log('   🌐 Browser: Will open automatically');
console.log('   🔍 Search: Will start in 3 seconds');
console.log('   ⚡ Live Updates: Every 2 seconds');
console.log('\n💡 Press Ctrl+C to stop all processes\n');

// Display periodic status
setInterval(() => {
    const now = new Date().toLocaleTimeString();
    console.log(`⏰ ${now} - Live dashboard running at ${DASHBOARD_URL}`);
}, 30000); // Every 30 seconds
