{"name": "clean-multisearch", "version": "1.0.0", "description": "Clean production-ready multi-account Acronis search engine", "main": "multi_account_search.js", "scripts": {"start": "node multi_account_search.js", "dashboard": "node search_results/serve_dashboard.js", "install-deps": "npm install"}, "dependencies": {"axios": "^1.6.0", "axios-cookiejar-support": "^4.0.7", "tough-cookie": "^4.1.3"}, "keywords": ["acronis", "search", "multi-account", "backup"], "author": "Dazzy Project", "license": "MIT"}