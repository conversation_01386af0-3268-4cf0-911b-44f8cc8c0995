const axios = require("axios");
const { <PERSON><PERSON><PERSON><PERSON> } = require("tough-cookie");
const { wrapper } = require("axios-cookiejar-support");

const email = "<EMAIL>";
const password = "Vertebra23";

// Helper function to generate unique request IDs
function generateRequestId() {
  return 'f' + Math.random().toString(16).substring(2, 17);
}

async function testSearcher() {
  const jar = new CookieJar();
  const client = wrapper(axios.create({ jar, withCredentials: true }));

  try {
    // Step 1: Login
    const loginResponse = await client.post("https://us4-cloud.acronis.com/api/1/login", {
      username: email,
      password: password
    }, {
      headers: {
        "Content-Type": "application/json",
        "X-Acronis-Api": "1",
        "X-Requested-With": "XMLHttpRequest",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
        "Origin": "https://us4-cloud.acronis.com",
        "Referer": "https://us4-cloud.acronis.com/login",
        "Accept": "application/json, text/plain, */*",
        "DNT": "1"
      }
    });

    if (loginResponse.status !== 200 || !loginResponse.data.mfa_status) {
      console.log("Login failed", loginResponse.data);
      return;
    }

    console.log("Login successful for", email);

    // Debug: Print cookies after login
    console.log("Cookies after login:", jar.getCookieStringSync("https://us4-cloud.acronis.com"));

    // Step 2: Navigate to UI to establish session (CRITICAL MISSING STEP)
    console.log("Navigating to UI to establish session...");
    const uiResponse = await client.get("https://us4-cloud.acronis.com/ui/", {
      headers: {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "DNT": "1",
        "Sec-Fetch-Site": "same-origin",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-User": "?1",
        "Sec-Fetch-Dest": "document",
        "Upgrade-Insecure-Requests": "1"
      }
    });

    console.log("UI navigation status:", uiResponse.status);
    console.log("Cookies after UI navigation:", jar.getCookieStringSync("https://us4-cloud.acronis.com"));

    // Step 3: Get webrestore link to obtain JWT token (NOW WITH PROPER SESSION)
    console.log("Requesting webrestore link...");
    const webrestoreResponse = await client.get("https://us4-cloud.acronis.com/bc/api/ams/links/webrestore", {
      headers: {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Referer": "https://us4-cloud.acronis.com/ui/",
        "DNT": "1",
        "Sec-Fetch-Site": "same-origin",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-User": "?1",
        "Sec-Fetch-Dest": "document",
        "Upgrade-Insecure-Requests": "1"
      },
      maxRedirects: 0,
      validateStatus: function (status) {
        return status >= 200 && status < 400; // Accept redirects
      }
    });

    if (webrestoreResponse.status !== 302) {
      console.log("Failed to get webrestore redirect. Status:", webrestoreResponse.status);
      console.log("Response:", webrestoreResponse.data);
      return;
    }

    const redirectUrl = webrestoreResponse.headers.location;
    console.log("Got JWT redirect URL:", redirectUrl);

    // Step 4: Follow JWT redirect to get JSESSIONID for search domain
    const autologinResponse = await client.get(redirectUrl, {
      headers: {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "DNT": "1"
      }
    });

    console.log("Autologin completed, status:", autologinResponse.status);
    console.log("Search domain cookies:", jar.getCookieStringSync("https://cloud-wr-us2.acronis.com"));

    // Step 5: Get list of all backup folders first
    console.log("Getting list of backup folders...");
    const backupsResponse = await client.get("https://cloud-wr-us2.acronis.com/ui/content", {
      headers: {
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "en-GB,en;q=0.9,hi-IN;q=0.8,hi;q=0.7,en-US;q=0.6,ru;q=0.5",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
        "Referer": "https://cloud-wr-us2.acronis.com/",
        "Sec-Fetch-Site": "same-origin",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Dest": "empty",
        "UI-REQUEST-ID": generateRequestId(),
        "DNT": "1"
      },
      params: {
        archiveId: "113//99DBC6FF-1353-4699-820F-3A372B5AC282/gr8dc",
        path: ""  // Root path to get all backup folders
      }
    });

    console.log("Found backup folders:", backupsResponse.data.data.length);

    // Filter only backup folders (directories that start with "Backup #")
    const backupFolders = backupsResponse.data.data.filter(item =>
      item.folderItem && item.name.startsWith("Backup #")
    );

    console.log("Backup folders found:", backupFolders.map(f => f.name));

    // Step 6: Search for "program" in each backup's C:/ drive
    const searchKeyword = "program";
    const allSearchResults = [];

    for (const backup of backupFolders) {
      console.log(`\n--- Searching in ${backup.name} ---`);

      try {
        // First, check if C: drive exists in this backup
        const driveCheckResponse = await client.get("https://cloud-wr-us2.acronis.com/ui/content", {
          headers: {
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "en-GB,en;q=0.9,hi-IN;q=0.8,hi;q=0.7,en-US;q=0.6,ru;q=0.5",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
            "Referer": "https://cloud-wr-us2.acronis.com/",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "UI-REQUEST-ID": generateRequestId(),
            "DNT": "1"
          },
          params: {
            archiveId: "113//99DBC6FF-1353-4699-820F-3A372B5AC282/gr8dc",
            path: backup.name
          }
        });

        // Look for C: drive in this backup
        const drives = driveCheckResponse.data.data.filter(item =>
          item.folderItem && item.name.includes("C:")
        );

        if (drives.length === 0) {
          console.log(`No C: drive found in ${backup.name}`);
          continue;
        }

        const cDrive = drives[0];
        console.log(`Found C: drive: ${cDrive.name}`);

        // Now search in the C:/ drive of this backup
        const searchResponse = await client.get("https://cloud-wr-us2.acronis.com/ui/search", {
          headers: {
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "en-GB,en;q=0.9,hi-IN;q=0.8,hi;q=0.7,en-US;q=0.6,ru;q=0.5",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
            "Referer": "https://cloud-wr-us2.acronis.com/",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "UI-REQUEST-ID": generateRequestId(),
            "DNT": "1"
          },
          params: {
            archiveId: "113//99DBC6FF-1353-4699-820F-3A372B5AC282/gr8dc",
            volumePath: cDrive.id,
            path: cDrive.id,
            searchText: searchKeyword
          }
        });

        if (searchResponse.data && searchResponse.data.data) {
          const results = searchResponse.data.data;
          console.log(`Found ${results.length} results in ${backup.name}`);

          // Add backup info to each result
          const resultsWithBackup = results.map(result => ({
            ...result,
            backupFolder: backup.name,
            backupDate: backup.backUpDate
          }));

          allSearchResults.push(...resultsWithBackup);

          // Show first few results for this backup
          if (results.length > 0) {
            console.log("Sample results:");
            results.slice(0, 3).forEach(result => {
              console.log(`  - ${result.name} (${result.itemType}) - ${result.id}`);
            });
          }
        }

      } catch (searchError) {
        console.log(`Error searching in ${backup.name}:`, searchError.message);
        if (searchError.response) {
          console.log(`  Status: ${searchError.response.status}`);
          console.log(`  Error: ${searchError.response.data?.text || searchError.response.data}`);
        }
      }

      // Small delay between requests to be respectful
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Step 7: Summary of all results
    console.log(`\n=== SEARCH SUMMARY ===`);
    console.log(`Keyword: "${searchKeyword}"`);
    console.log(`Total results found: ${allSearchResults.length}`);
    console.log(`Searched in ${backupFolders.length} backup folders`);

    if (allSearchResults.length > 0) {
      console.log("\nAll results:");
      allSearchResults.forEach((result, index) => {
        console.log(`${index + 1}. [${result.backupFolder}] ${result.name} - ${result.id}`);
      });
    }

  } catch (error) {
    console.error("Error during test:", error.message);
    if (error.response) {
      console.error("Response status:", error.response.status);
      console.error("Response headers:", error.response.headers);
      console.error("Response data:", error.response.data);
      console.error("Request URL:", error.config?.url);
    }
  }
}

// Run the test
testSearcher();
