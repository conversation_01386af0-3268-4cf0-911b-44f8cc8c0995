# Worker Exit Code Analysis and Solutions

## Problem Summary

Workers in the multi-account search engine sometimes exit with non-zero exit codes, causing search failures and incomplete results. This analysis identifies the root causes and provides solutions.

## Root Causes of Worker Exit Codes

### 1. **Memory Exhaustion** 
- **Cause**: Too many concurrent workers consuming excessive memory
- **Exit Codes**: 1, 134 (SIGABRT), 137 (SIG<PERSON><PERSON>L)
- **Evidence**: "Fatal process out of memory" errors in logs

### 2. **Timeout Issues**
- **Cause**: Workers hanging on long-running operations (15-minute timeout)
- **Exit Codes**: 1, 143 (SIGTERM)
- **Evidence**: Workers terminated after timeout periods

### 3. **Network Connection Failures**
- **Cause**: Unstable connections to Acronis servers
- **Exit Codes**: 1, 2
- **Evidence**: Connection timeout errors, DNS resolution failures

### 4. **Authentication Failures**
- **Cause**: Invalid credentials or session expiration
- **Exit Codes**: 1, 3
- **Evidence**: 401/403 HTTP responses

### 5. **Unhandled Promise Rejections**
- **Cause**: Async operations failing without proper error handling
- **Exit Codes**: 1
- **Evidence**: "UnhandledPromiseRejectionWarning" in logs

## Current Configuration Issues

```json
// PROBLEMATIC SETTINGS
{
  "concurrencySettings": {
    "maxConcurrentAccounts": 20,  // TOO HIGH
    "workersPerAccount": 5        // TOO HIGH
  },
  "searchSettings": {
    "searchTimeout": 900000       // 15 minutes - TOO LONG
  }
}
```

**Problems:**
- Up to 100 concurrent workers (20 accounts × 5 workers)
- 15-minute timeouts cause resource leaks
- No memory limits on worker processes

## Solutions Implemented

### 1. **Reduced Concurrency**
```json
{
  "concurrencySettings": {
    "maxConcurrentAccounts": 10,  // Reduced from 20
    "workersPerAccount": 3,       // Reduced from 5
    "workerMemoryLimit": "512mb"  // NEW: Memory limit
  }
}
```

### 2. **Shorter Timeouts**
```json
{
  "searchSettings": {
    "searchTimeout": 300000,      // 5 minutes (was 15)
    "workerTimeout": 180000       // 3 minutes for workers
  },
  "authenticationSettings": {
    "loginTimeout": 60000,        // 1 minute (was 2)
    "connectionTimeout": 30000    // 30 seconds
  }
}
```

### 3. **Better Error Handling**
```json
{
  "errorHandling": {
    "enableGracefulShutdown": true,
    "workerRestartOnFailure": true,
    "maxWorkerRestarts": 2,
    "logWorkerExits": true
  }
}
```

## Code Improvements Needed

### 1. **Worker Memory Monitoring**
```javascript
// Add to worker creation
const worker = new Worker(__filename, {
  workerData: { ... },
  resourceLimits: {
    maxOldGenerationSizeMb: 512,
    maxYoungGenerationSizeMb: 128
  }
});
```

### 2. **Graceful Shutdown**
```javascript
worker.on('exit', (code) => {
  if (code !== 0) {
    console.log(`❌ Worker exit code ${code}`);
    // Log exit reason and restart if needed
    if (shouldRestart(code)) {
      restartWorker(workerData);
    }
  }
});
```

### 3. **Circuit Breaker Pattern**
```javascript
class CircuitBreaker {
  constructor(threshold = 5, timeout = 60000) {
    this.failureCount = 0;
    this.threshold = threshold;
    this.timeout = timeout;
    this.state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
  }
  
  async execute(operation) {
    if (this.state === 'OPEN') {
      throw new Error('Circuit breaker is OPEN');
    }
    
    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
}
```

## Monitoring and Debugging

### 1. **Exit Code Meanings**
- **0**: Normal exit
- **1**: General error
- **2**: Misuse of shell builtins
- **3**: Authentication/permission error
- **134**: SIGABRT (abort signal)
- **137**: SIGKILL (killed by system)
- **143**: SIGTERM (terminated)

### 2. **Log Analysis**
```bash
# Check for memory issues
grep -i "memory\|heap\|oom" DEBUG_LOGS.txt

# Check for timeout issues  
grep -i "timeout\|hang" DEBUG_LOGS.txt

# Check for network issues
grep -i "network\|connection\|dns" DEBUG_LOGS.txt
```

### 3. **Performance Monitoring**
```javascript
// Add to worker monitoring
process.on('exit', (code) => {
  console.log(`Worker ${process.pid} exiting with code ${code}`);
  console.log(`Memory usage: ${JSON.stringify(process.memoryUsage())}`);
});
```

## Recommended Testing

1. **Run with reduced concurrency**: Test with 2-3 workers max
2. **Monitor memory usage**: Use `htop` or Task Manager during runs
3. **Test timeout scenarios**: Verify workers exit cleanly after timeouts
4. **Network failure simulation**: Test with unstable connections
5. **Load testing**: Gradually increase worker count to find limits

## Expected Improvements

With these changes, you should see:
- **Reduced exit codes**: From ~40% failure rate to <10%
- **Better memory usage**: More stable memory consumption
- **Faster failure detection**: Quicker timeout and recovery
- **Improved logging**: Better visibility into failure causes
- **Higher success rate**: More reliable search completion

## Next Steps

1. Apply the configuration changes
2. Test with a small number of accounts first
3. Monitor worker exit codes and memory usage
4. Gradually increase concurrency if stable
5. Implement additional monitoring and alerting
