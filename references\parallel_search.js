const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const fs = require('fs').promises;
const path = require('path');
const axios = require("axios");
const { CookieJar } = require("tough-cookie");
const { wrapper } = require("axios-cookiejar-support");

// const email = "<EMAIL>";
// const password = "Vertebra23";

// Helper function to generate unique request IDs
function generateRequestId() {
  return 'f' + Math.random().toString(16).substring(2, 17);
}

// Create results directory if it doesn't exist
async function ensureResultsDir() {
  const resultsDir = path.join(__dirname, 'search_results');
  try {
    await fs.access(resultsDir);
  } catch {
    await fs.mkdir(resultsDir, { recursive: true });
  }
  return resultsDir;
}

// Save search results to JSON file
async function saveSearchResults(searchData) {
  const resultsDir = await ensureResultsDir();
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `search_${searchData.keyword}_${timestamp}.json`;
  const filepath = path.join(resultsDir, filename);
  
  await fs.writeFile(filepath, JSON.stringify(searchData, null, 2));
  console.log(`📁 Results saved to: ${filepath}`);
  return filepath;
}

// Generate HTML dashboard
async function generateDashboard(searchData) {
  const resultsDir = await ensureResultsDir();
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `dashboard_${searchData.keyword}_${timestamp}.html`;
  const filepath = path.join(resultsDir, filename);
  
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Acronis Search Dashboard - ${searchData.keyword}</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f7fa; }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; color: #667eea; }
        .stat-label { color: #666; margin-top: 5px; }
        .backup-section { background: white; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-bottom: 20px; overflow: hidden; }
        .backup-header { background: #667eea; color: white; padding: 15px 20px; font-weight: bold; cursor: pointer; }
        .backup-header:hover { background: #5a6fd8; }
        .backup-content { padding: 20px; display: none; }
        .backup-content.active { display: block; }
        .result-item { border-left: 4px solid #667eea; padding: 15px; margin: 10px 0; background: #f8f9ff; border-radius: 5px; }
        .result-name { font-weight: bold; color: #333; margin-bottom: 5px; }
        .result-path { color: #666; font-size: 0.9em; word-break: break-all; }
        .result-meta { display: flex; gap: 15px; margin-top: 10px; font-size: 0.8em; color: #888; }
        .no-results { text-align: center; color: #666; padding: 40px; }
        .search-info { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-bottom: 30px; }
        .performance { background: #e8f5e8; border-left: 4px solid #4caf50; padding: 15px; margin: 20px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Acronis Search Results</h1>
            <p>Search for: "<strong>${searchData.keyword}</strong>" | Generated: ${new Date(searchData.timestamp).toLocaleString()}</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">${searchData.totalResults}</div>
                <div class="stat-label">Total Results</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${searchData.backupsSearched}</div>
                <div class="stat-label">Backups Searched</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${searchData.backupsWithResults}</div>
                <div class="stat-label">Backups with Results</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${searchData.searchDuration}s</div>
                <div class="stat-label">Search Duration</div>
            </div>
        </div>
        
        <div class="search-info">
            <h3>📊 Search Summary</h3>
            <p><strong>Keyword:</strong> ${searchData.keyword}</p>
            <p><strong>Archive ID:</strong> ${searchData.archiveId}</p>
            <p><strong>Search Method:</strong> ${searchData.parallel ? 'Parallel Workers' : 'Sequential'}</p>
            ${searchData.parallel ? `<div class="performance">⚡ Parallel processing used ${searchData.workerCount} workers for faster results!</div>` : ''}
        </div>
        
        <div class="results">
            ${searchData.results.map(backup => `
                <div class="backup-section">
                    <div class="backup-header" onclick="toggleBackup('${backup.backupName}')">
                        📁 ${backup.backupName} (${backup.results.length} results)
                        <span style="float: right;">▼</span>
                    </div>
                    <div class="backup-content" id="${backup.backupName}">
                        ${backup.results.length > 0 ? 
                            backup.results.map(result => `
                                <div class="result-item">
                                    <div class="result-name">📄 ${result.name}</div>
                                    <div class="result-path">${result.id}</div>
                                    <div class="result-meta">
                                        <span>📏 Size: ${formatFileSize(result.size)}</span>
                                        <span>📅 Modified: ${new Date(result.modifiedDate).toLocaleDateString()}</span>
                                        <span>🏷️ Type: ${result.itemType}</span>
                                    </div>
                                </div>
                            `).join('') 
                            : '<div class="no-results">No results found in this backup</div>'
                        }
                    </div>
                </div>
            `).join('')}
        </div>
    </div>
    
    <script>
        function toggleBackup(backupName) {
            const content = document.getElementById(backupName);
            const header = content.previousElementSibling;
            const arrow = header.querySelector('span');
            
            if (content.classList.contains('active')) {
                content.classList.remove('active');
                arrow.textContent = '▼';
            } else {
                content.classList.add('active');
                arrow.textContent = '▲';
            }
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // Auto-expand first backup with results
        document.addEventListener('DOMContentLoaded', function() {
            const firstBackupWithResults = document.querySelector('.backup-content');
            if (firstBackupWithResults) {
                firstBackupWithResults.classList.add('active');
                const arrow = firstBackupWithResults.previousElementSibling.querySelector('span');
                arrow.textContent = '▲';
            }
        });
    </script>
</body>
</html>`;
  
  await fs.writeFile(filepath, html);
  console.log(`🌐 Dashboard saved to: ${filepath}`);
  return filepath;
}

// Recursive function to find volumes in archive structure
async function findVolumesRecursively(client, archiveId, currentPath, maxDepth = 5, currentDepth = 0) {
  if (currentDepth >= maxDepth) {
    return [];
  }

  // Add small delay to prevent rate limiting
  if (currentDepth > 0) {
    await new Promise(resolve => setTimeout(resolve, 200));
  }

  try {
    const response = await client.get("https://cloud-wr-us2.acronis.com/ui/content", {
      headers: {
        "Accept": "application/json, text/plain, */*",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Referer": "https://cloud-wr-us2.acronis.com/",
        "UI-REQUEST-ID": generateRequestId(),
      },
      params: {
        archiveId: archiveId,
        path: currentPath
      }
    });

    if (!response.data || !response.data.data) {
      return [];
    }

    const items = response.data.data;
    const volumes = [];

    // Look for volume items using proper API properties
    const driveItems = items.filter(item =>
      item.folderItem &&
      item.itemType === "MountPoint" &&
      item.itemGroup === "volume"
    );

    // Add found volumes
    if (driveItems.length > 0) {
      console.log(`🔍 Found ${driveItems.length} volumes at path "${currentPath}": ${driveItems.map(v => v.name).join(', ')}`);
    }
    volumes.push(...driveItems);

    // If no volumes found at this level, recursively search in folders
    if (volumes.length === 0) {
      const folders = items.filter(item => item.folderItem && !item.name.includes("."));

      for (const folder of folders.slice(0, 3)) { // Limit to first 3 folders to avoid infinite recursion
        const subPath = currentPath ? `${currentPath}/${folder.name}` : folder.name;
        const subVolumes = await findVolumesRecursively(client, archiveId, subPath, maxDepth, currentDepth + 1);
        volumes.push(...subVolumes);
      }
    }

    return volumes;
  } catch (error) {
    console.log(`⚠️  Error exploring path ${currentPath}: ${error.message}`);
    return [];
  }
}

// Worker thread function for searching individual backups
async function searchWorker(workerData) {
  const { backup, searchKeyword, authCookies, archiveId } = workerData;

  console.log(`🐛 DEBUG: Starting worker for backup ${backup.name}`);

  try {
    // Create new client with auth cookies
    const jar = new CookieJar();

    console.log(`🐛 DEBUG: Restoring ${authCookies.length} cookies for ${backup.name}`);

    // Restore cookies from main thread
    for (const cookie of authCookies) {
      try {
        jar.setCookieSync(cookie.cookie, cookie.url);
      } catch (cookieError) {
        console.log(`🐛 DEBUG: Cookie error for ${backup.name}: ${cookieError.message}`);
      }
    }

    const client = wrapper(axios.create({
      jar,
      withCredentials: true,
      timeout: 900000, // 15 minutes timeout for search operations
      validateStatus: function (status) {
        return status >= 200 && status < 300; // Accept only 2xx status codes
      }
    }));

    console.log(`🐛 DEBUG: Client created for ${backup.name} with 15min timeout`);

    // Find volumes recursively in the backup structure
    console.log(`🔍 Exploring backup structure: ${backup.name}`);
    const volumes = await findVolumesRecursively(client, archiveId, backup.name);

    if (volumes.length === 0) {
      console.log(`⚠️  No volumes found in ${backup.name}, trying direct search API fallback...`);

      // Fallback: Use searcher API directly on the backup/drive itself
      try {
        console.log(`🔍 Attempting direct search in backup: ${backup.name}`);
        console.log(`   📋 Archive ID: ${archiveId}`);
        console.log(`   📁 Backup Path: ${backup.id || backup.name}`);
        console.log(`   🔎 Search Text: ${searchKeyword}`);

        const requestId = generateRequestId();
        console.log(`🐛 DEBUG: Making direct search request with ID: ${requestId}`);

        // Use the backup ID or name as the search path
        const searchPath = backup.id || backup.name;
        const searchParams = {
          archiveId: archiveId,
          volumePath: searchPath,
          path: searchPath,
          searchText: searchKeyword
        };

        console.log(`🐛 DEBUG: Direct search params:`, JSON.stringify(searchParams, null, 2));

        const searchResponse = await client.get("https://cloud-wr-us2.acronis.com/ui/search", {
          headers: {
            "Accept": "application/json, text/plain, */*",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Referer": "https://cloud-wr-us2.acronis.com/",
            "UI-REQUEST-ID": requestId,
          },
          params: searchParams
        });

        console.log(`🐛 DEBUG: Direct search response received for ${backup.name}, status: ${searchResponse.status}`);

        if (searchResponse.status === 200 && searchResponse.data && searchResponse.data.data) {
          const searchResults = searchResponse.data.data;
          console.log(`✅ Direct search found ${searchResults.length} results in ${backup.name}`);

          const processedResults = searchResults.map(item => ({
            name: item.name,
            displayName: item.displayName || item.name,
            itemType: item.itemType,
            id: item.id,
            backupFolder: backup.name,
            archiveName: archiveId.split('/').pop(),
            accountEmail: "unknown" // Will be set by caller
          }));

          return {
            backupName: backup.name,
            results: processedResults,
            error: null,
            message: `Direct search found ${processedResults.length} results`
          };
        } else {
          console.log(`⚠️  Direct search API returned no results for ${backup.name}`);
        }
      } catch (directSearchError) {
        console.log(`❌ Direct search failed for ${backup.name}: ${directSearchError.message}`);
      }

      // If direct search also fails, return the original "no volumes" message
      return {
        backupName: backup.name,
        results: [],
        error: null,
        message: "No volumes found in backup structure and direct search failed"
      };
    }

    console.log(`📁 Found ${volumes.length} volumes in ${backup.name}:`);
    volumes.forEach((v, index) => {
      console.log(`   ${index + 1}. ${v.name} (ID: ${v.id})`);
    });

    let allResults = [];

    // Search in each found volume
    for (const volume of volumes) {
      try {
        console.log(`🔍 Searching in volume: ${volume.name}`);
        console.log(`   📋 Archive ID: ${archiveId}`);
        console.log(`   📁 Volume Path: ${volume.id}`);
        console.log(`   🔎 Search Text: ${searchKeyword}`);

        const requestId = generateRequestId();
        console.log(`🐛 DEBUG: Making search request with ID: ${requestId}`);

        const searchParams = {
          archiveId: archiveId,
          volumePath: volume.id,
          path: volume.id,
          searchText: searchKeyword
        };

        console.log(`🐛 DEBUG: Search params:`, JSON.stringify(searchParams, null, 2));

        const searchResponse = await client.get("https://cloud-wr-us2.acronis.com/ui/search", {
          headers: {
            "Accept": "application/json, text/plain, */*",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Referer": "https://cloud-wr-us2.acronis.com/",
            "UI-REQUEST-ID": requestId,
          },
          params: searchParams
        });

        console.log(`🐛 DEBUG: Search response received for ${volume.name}, status: ${searchResponse.status}`);

        // Check for successful 200 response
        if (searchResponse.status !== 200) {
          console.log(`⚠️  Search API returned status ${searchResponse.status} for volume ${volume.name}`);
          continue;
        }

        console.log(`✅ Search API returned 200 OK for volume ${volume.name}`);
        const results = searchResponse.data?.data || [];

        // Add volume info to each result
        const volumeResults = results.map(result => ({
          ...result,
          backupFolder: backup.name,
          backupDate: backup.backUpDate,
          volumeName: volume.name,
          volumePath: volume.id
        }));

        allResults.push(...volumeResults);
        console.log(`✅ Found ${results.length} results in ${volume.name}`);

        // Longer delay between volume searches to prevent socket hang up
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (volumeError) {
        console.log(`🐛 DEBUG: Full error details for volume ${volume.name}:`);
        console.log(`🐛 DEBUG: Error message: ${volumeError.message}`);
        console.log(`🐛 DEBUG: Error code: ${volumeError.code}`);
        console.log(`🐛 DEBUG: Error stack: ${volumeError.stack}`);
        if (volumeError.response) {
          console.log(`🐛 DEBUG: Response status: ${volumeError.response.status}`);
          console.log(`🐛 DEBUG: Response headers:`, volumeError.response.headers);
          console.log(`🐛 DEBUG: Response data:`, volumeError.response.data);
        }
        if (volumeError.request) {
          console.log(`🐛 DEBUG: Request details:`, {
            method: volumeError.request.method,
            url: volumeError.request.url,
            headers: volumeError.request.headers
          });
        }
        console.log(`⚠️  Error searching volume ${volume.name}: ${volumeError.message}`);
        continue;
      }
    }

    return {
      backupName: backup.name,
      results: allResults,
      error: null,
      message: `Found ${allResults.length} results across ${volumes.length} volumes`
    };

  } catch (error) {
    console.log(`🐛 DEBUG: Main worker error for backup ${backup.name}:`);
    console.log(`🐛 DEBUG: Error message: ${error.message}`);
    console.log(`🐛 DEBUG: Error code: ${error.code}`);
    console.log(`🐛 DEBUG: Error stack: ${error.stack}`);

    return {
      backupName: backup.name,
      results: [],
      error: error.message,
      message: `Error: ${error.message}`
    };
  }
}

module.exports = {
  generateRequestId,
  ensureResultsDir,
  saveSearchResults,
  generateDashboard,
  searchWorker
};
