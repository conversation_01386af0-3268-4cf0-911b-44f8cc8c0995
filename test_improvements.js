#!/usr/bin/env node

/**
 * Test script to verify all improvements and bug fixes
 */

const fs = require('fs').promises;
const path = require('path');

async function testImprovements() {
    console.log('🧪 Testing Multi-Account Search Engine Improvements');
    console.log('==================================================');
    
    let allTestsPassed = true;
    
    try {
        // Test 1: Configuration loading
        console.log('\n1. Testing configuration loading...');
        const { loadConfig } = require('./multi_account_search.js');
        const config = await loadConfig();
        console.log('✅ Configuration loaded successfully');
        console.log(`   Keywords: ${config.searchSettings.keywords.join(', ')}`);
        
        // Test 2: Account loading with improved format
        console.log('\n2. Testing account loading...');
        const { loadAccounts } = require('./multi_account_search.js');
        const accounts = await loadAccounts();
        console.log(`✅ Loaded ${accounts.length} accounts`);
        
        if (accounts.length > 0) {
            console.log(`   First account: ${accounts[0].email}`);
        }
        
        // Test 3: Check accounts.txt has improved guidance
        console.log('\n3. Testing accounts.txt improvements...');
        const accountsContent = await fs.readFile('accounts.txt', 'utf8');
        if (accountsContent.includes('IMPORTANT NOTES')) {
            console.log('✅ accounts.txt has improved guidance');
        } else {
            console.log('❌ accounts.txt missing improved guidance');
            allTestsPassed = false;
        }
        
        // Test 4: Check README has troubleshooting section
        console.log('\n4. Testing README improvements...');
        const readmeContent = await fs.readFile('README.md', 'utf8');
        if (readmeContent.includes('Troubleshooting')) {
            console.log('✅ README has troubleshooting section');
        } else {
            console.log('❌ README missing troubleshooting section');
            allTestsPassed = false;
        }
        
        // Test 5: Check if result file structure is correct
        console.log('\n5. Testing result file structure...');
        try {
            const resultPath = path.join('search_results', 'RESULT.json');
            const resultExists = await fs.access(resultPath).then(() => true).catch(() => false);
            
            if (resultExists) {
                const resultContent = await fs.readFile(resultPath, 'utf8');
                const result = JSON.parse(resultContent);
                
                if (result.accounts && Array.isArray(result.accounts)) {
                    console.log('✅ Result file has correct structure');
                    console.log(`   Total accounts: ${result.totalAccounts}`);
                    console.log(`   Successful accounts: ${result.successfulAccounts}`);
                    console.log(`   Failed accounts: ${result.failedAccounts}`);
                } else {
                    console.log('❌ Result file has incorrect structure');
                    allTestsPassed = false;
                }
            } else {
                console.log('ℹ️  No result file found (normal if no searches run yet)');
            }
        } catch (error) {
            console.log(`⚠️  Could not read result file: ${error.message}`);
        }
        
        // Test 6: Check dashboard files exist
        console.log('\n6. Testing dashboard files...');
        const dashboardFiles = [
            'search_results/serve_dashboard.js',
            'search_results/flexible_dashboard.html'
        ];
        
        for (const file of dashboardFiles) {
            const exists = await fs.access(file).then(() => true).catch(() => false);
            if (exists) {
                console.log(`✅ ${file} exists`);
            } else {
                console.log(`❌ ${file} missing`);
                allTestsPassed = false;
            }
        }
        
        // Test 7: Test debug logging function
        console.log('\n7. Testing debug logging...');
        const { logDebug } = require('./multi_account_search.js');
        logDebug('Test debug message', 'INFO');
        logDebug('Test error message', 'ERROR', '<EMAIL>');
        logDebug('Test success message', 'SUCCESS', '<EMAIL>');
        console.log('✅ Debug logging working');
        
        // Test 8: Check package.json scripts
        console.log('\n8. Testing package.json scripts...');
        const packageContent = await fs.readFile('package.json', 'utf8');
        const packageJson = JSON.parse(packageContent);
        
        if (packageJson.scripts && packageJson.scripts.start && packageJson.scripts.dashboard) {
            console.log('✅ Package.json has required scripts');
            console.log(`   Start script: ${packageJson.scripts.start}`);
            console.log(`   Dashboard script: ${packageJson.scripts.dashboard}`);
        } else {
            console.log('❌ Package.json missing required scripts');
            allTestsPassed = false;
        }
        
        // Summary
        console.log('\n📊 Test Summary:');
        if (allTestsPassed) {
            console.log('🎉 All tests passed! The improvements are working correctly.');
            console.log('\n✅ Key improvements verified:');
            console.log('   - Better error handling for accounts with no archives');
            console.log('   - Improved result file saving and structure');
            console.log('   - Enhanced user guidance in accounts.txt');
            console.log('   - Troubleshooting section in README');
            console.log('   - Proper dashboard functionality');
            console.log('   - Debug logging system');
        } else {
            console.log('❌ Some tests failed. Please check the issues above.');
        }
        
        console.log('\n🚀 To run the full system:');
        console.log('   1. npm start          # Run the search');
        console.log('   2. npm run dashboard  # Start the dashboard');
        console.log('   3. Open http://localhost:8080');
        
    } catch (error) {
        console.error('❌ Test failed with error:', error.message);
        allTestsPassed = false;
    }
    
    return allTestsPassed;
}

if (require.main === module) {
    testImprovements().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = { testImprovements };
