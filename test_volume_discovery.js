#!/usr/bin/env node

/**
 * Test script to debug volume discovery in backup folders
 */

const { searchWorker, findVolumesRecursively, generateRequestId } = require('./parallel_search.js');
const axios = require('axios');
const { wrapper } = require('axios-cookiejar-support');
const { CookieJar } = require('tough-cookie');

async function testVolumeDiscovery() {
    console.log('🔍 Testing Volume Discovery');
    console.log('===========================');

    // Test credentials (you'll need to update these)
    const authCookies = [
        { name: 'session', value: 'your-session-cookie' },
        // Add your actual auth cookies here
    ];

    const archiveId = "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR";
    const testBackup = { name: "Backup #1", id: "Backup #1" };

    try {
        const jar = new CookieJar();
        authCookies.forEach(cookie => {
            jar.setCookieSync(`${cookie.name}=${cookie.value}`, "https://cloud-wr-us2.acronis.com");
        });

        const client = wrapper(axios.create({
            jar,
            withCredentials: true,
            timeout: 30000
        }));

        console.log(`🔍 Testing volume discovery for: ${testBackup.name}`);
        console.log(`📋 Archive ID: ${archiveId}`);

        const volumes = await findVolumesRecursively(client, archiveId, testBackup.name);
        
        console.log(`\n📊 Volume Discovery Results:`);
        console.log(`   Found ${volumes.length} volumes`);
        
        if (volumes.length > 0) {
            volumes.forEach((volume, index) => {
                console.log(`   ${index + 1}. Name: "${volume.name}"`);
                console.log(`      ID: "${volume.id}"`);
                console.log(`      Type: "${volume.itemType}"`);
                console.log('');
            });
        } else {
            console.log('   ❌ No volumes found - this explains why we get mount points!');
        }

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.log('\n💡 This test requires valid authentication cookies.');
        console.log('   You can get them by:');
        console.log('   1. Opening browser dev tools');
        console.log('   2. Going to Acronis cloud');
        console.log('   3. Copying the session cookies');
        console.log('   4. Updating this script with real cookies');
    }
}

async function demonstrateExpectedBehavior() {
    console.log('\n🎯 Expected vs Actual Behavior');
    console.log('==============================');
    
    console.log('✅ EXPECTED: Volume discovery should find:');
    console.log('   1. "Data (D:)" (ID: "Data (D:)")');
    console.log('   2. "Windows-SSD (C:)" (ID: "Windows-SSD (C:)")');
    console.log('   3. "SYSTEM_DRV" (ID: "SYSTEM_DRV")');
    console.log('   4. "WINRE_DRV" (ID: "WINRE_DRV")');
    
    console.log('\n❌ ACTUAL: Volume discovery finds 0 volumes');
    console.log('   → Falls back to direct search with backup folder name');
    console.log('   → volumePath: "Backup #1" instead of "Data (D:)"');
    console.log('   → Returns mount points instead of files');
    
    console.log('\n🔧 SOLUTION: Fix volume discovery criteria');
    console.log('   → Check if items have different itemType values');
    console.log('   → Look for MountPoint items (added in latest update)');
    console.log('   → Verify the folder structure navigation');
}

if (require.main === module) {
    testVolumeDiscovery()
        .then(() => demonstrateExpectedBehavior())
        .catch(error => {
            console.error('❌ Script failed:', error.message);
        });
}

module.exports = { testVolumeDiscovery };
