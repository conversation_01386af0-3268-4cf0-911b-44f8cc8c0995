const axios = require('axios');
const { wrapper } = require('axios-cookiejar-support');
const { <PERSON><PERSON><PERSON><PERSON> } = require('tough-cookie');

// Test script <NAME_EMAIL> account
async function testCoteMikeAccount() {
    const email = "<EMAIL>";
    const password = "M@lapascua8888";
    
    console.log(`🧪 Testing account: ${email}`);
    console.log('=' .repeat(50));
    
    try {
        // Step 1: Authentication
        console.log('🔐 Step 1: Authenticating...');
        
        const jar = new CookieJar();
        const client = wrapper(axios.create({
            jar,
            withCredentials: true,
            timeout: 30000
        }));

        // Login
        const loginResponse = await client.post("https://us4-cloud.acronis.com/api/1/login", {
            username: email,
            password: password
        }, {
            headers: {
                "Content-Type": "application/json",
                "X-Acronis-Api": "1",
                "X-Requested-With": "XMLHttpRequest",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Origin": "https://us4-cloud.acronis.com",
                "Referer": "https://us4-cloud.acronis.com/login",
                "Accept": "application/json, text/plain, */*",
                "DNT": "1"
            }
        });

        if (loginResponse.status !== 200) {
            throw new Error("Login failed");
        }
        console.log('✅ Login successful');

        // Navigate to UI
        await client.get("https://us4-cloud.acronis.com/ui/", {
            headers: {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "DNT": "1"
            }
        });

        // Get webrestore link
        const webrestoreResponse = await client.get("https://us4-cloud.acronis.com/bc/api/ams/links/webrestore", {
            headers: {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "Referer": "https://us4-cloud.acronis.com/ui/",
                "DNT": "1"
            },
            maxRedirects: 0,
            validateStatus: function (status) {
                return status >= 200 && status < 400;
            }
        });

        if (webrestoreResponse.status !== 302) {
            throw new Error("Failed to get webrestore redirect");
        }

        const redirectUrl = webrestoreResponse.headers.location;
        console.log('✅ Got webrestore redirect URL');

        // Follow JWT redirect
        await client.get(redirectUrl, {
            headers: {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "DNT": "1"
            }
        });
        console.log('✅ Authentication completed');

        // Step 2: Discover archives
        console.log('\n📦 Step 2: Discovering archives...');
        
        const boxesResponse = await client.get("https://cloud-wr-us2.acronis.com/ui/boxes", {
            headers: {
                "Accept": "application/json, text/plain, */*",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Referer": "https://cloud-wr-us2.acronis.com/",
                "UI-REQUEST-ID": 'f' + Math.random().toString(16).substring(2, 17),
                "DNT": "1"
            }
        });

        console.log(`📊 Archives discovered: ${boxesResponse.data.length}`);
        boxesResponse.data.forEach((archive, index) => {
            console.log(`   ${index + 1}. ${archive.id} (${archive.displayName})`);
        });

        // Step 3: Test archive access
        if (boxesResponse.data.length > 0) {
            const archiveId = boxesResponse.data[0].id;
            console.log(`\n🔍 Step 3: Testing archive access for: ${archiveId}`);
            
            try {
                const contentResponse = await client.get("https://cloud-wr-us2.acronis.com/ui/content", {
                    headers: {
                        "Accept": "application/json, text/plain, */*",
                        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                        "Referer": "https://cloud-wr-us2.acronis.com/",
                        "UI-REQUEST-ID": 'f' + Math.random().toString(16).substring(2, 17),
                    },
                    params: {
                        archiveId: archiveId,
                        path: ""
                    }
                });

                console.log(`✅ Archive access successful`);
                console.log(`📁 Content items found: ${contentResponse.data.data?.length || 0}`);
                
                if (contentResponse.data.data && contentResponse.data.data.length > 0) {
                    console.log('📋 Content items:');
                    contentResponse.data.data.slice(0, 5).forEach((item, index) => {
                        console.log(`   ${index + 1}. ${item.name} (${item.itemType})`);
                    });
                }

            } catch (contentError) {
                console.log(`❌ Archive access failed: ${contentError.message}`);
                if (contentError.response) {
                    console.log(`   Status: ${contentError.response.status}`);
                    console.log(`   Status Text: ${contentError.response.statusText}`);
                }
                
                console.log('\n💡 Possible reasons:');
                console.log('   - Archive is offline or moved');
                console.log('   - .tibx format requires different access method');
                console.log('   - Account permissions insufficient');
                console.log('   - Archive is in a different state/status');
            }
        }

    } catch (error) {
        console.log(`❌ Test failed: ${error.message}`);
        if (error.response) {
            console.log(`   Status: ${error.response.status}`);
            console.log(`   Status Text: ${error.response.statusText}`);
        }
    }
}

// Run the test
if (require.main === module) {
    testCoteMikeAccount().catch(console.error);
}

module.exports = { testCoteMikeAccount };
