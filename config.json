{"searchSettings": {"keywords": ["program"], "searchTimeout": 900000, "enableProgressiveUpdates": true}, "concurrencySettings": {"maxConcurrentAccounts": 20, "workersPerAccount": 5, "delayBetweenAccounts": 2000, "delayBetweenBatches": 3000}, "outputSettings": {"resultFile": "search_results/RESULT.json", "debugLogFile": "DEBUG_LOGS.txt", "enableDetailedLogging": true, "preserveIndividualResults": false}, "authenticationSettings": {"loginTimeout": 120000, "retryAttempts": 3, "retryDelay": 10000}, "archiveSettings": {"discoverAllArchives": true, "searchAllVolumes": true, "volumeTimeout": 900000}, "dashboardSettings": {"enableAutoRefresh": true, "refreshInterval": 5000, "showAccountBreakdown": true}}