{"searchSettings": {"keywords": ["program"], "searchTimeout": 300000, "enableProgressiveUpdates": true, "workerTimeout": 180000, "maxRetries": 2}, "concurrencySettings": {"maxConcurrentAccounts": 10, "workersPerAccount": 3, "delayBetweenAccounts": 3000, "delayBetweenBatches": 5000, "workerMemoryLimit": "512mb"}, "outputSettings": {"resultFile": "search_results/RESULT.json", "debugLogFile": "DEBUG_LOGS.txt", "enableDetailedLogging": true, "preserveIndividualResults": false}, "authenticationSettings": {"loginTimeout": 60000, "retryAttempts": 3, "retryDelay": 5000, "connectionTimeout": 30000}, "archiveSettings": {"discoverAllArchives": true, "searchAllVolumes": true, "volumeTimeout": 180000, "maxVolumeDepth": 3}, "dashboardSettings": {"enableAutoRefresh": true, "refreshInterval": 5000, "showAccountBreakdown": true}, "errorHandling": {"enableGracefulShutdown": true, "workerRestartOnFailure": true, "maxWorkerRestarts": 2, "logWorkerExits": true}}