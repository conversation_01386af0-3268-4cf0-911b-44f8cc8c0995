const fs = require('fs').promises;
const path = require('path');
const { exec } = require('child_process');

async function listSearchResults() {
  const resultsDir = path.join(__dirname, 'search_results');
  
  try {
    const files = await fs.readdir(resultsDir);
    const jsonFiles = files.filter(f => f.endsWith('.json')).sort().reverse();
    const htmlFiles = files.filter(f => f.endsWith('.html')).sort().reverse();
    
    console.log("🔍 Available Search Results:");
    console.log("=" .repeat(50));
    
    if (jsonFiles.length === 0) {
      console.log("❌ No search results found. Run fast_search.js first.");
      return;
    }
    
    console.log("\n📊 Recent Searches:");
    for (let i = 0; i < Math.min(5, jsonFiles.length); i++) {
      const file = jsonFiles[i];
      const filePath = path.join(resultsDir, file);
      const data = JSON.parse(await fs.readFile(filePath, 'utf8'));
      
      console.log(`${i + 1}. ${file}`);
      console.log(`   🎯 Keyword: "${data.keyword}"`);
      console.log(`   📅 Date: ${new Date(data.timestamp).toLocaleString()}`);
      console.log(`   📄 Results: ${data.totalResults}`);
      console.log(`   ⏱️  Duration: ${data.searchDuration}s`);
      console.log("");
    }
    
    console.log("\n🌐 Recent Dashboards:");
    for (let i = 0; i < Math.min(3, htmlFiles.length); i++) {
      const file = htmlFiles[i];
      console.log(`${i + 1}. ${file}`);
    }
    
    if (htmlFiles.length > 0) {
      console.log(`\n💡 To open latest dashboard: node view_results.js open`);
      console.log(`💡 To open specific dashboard: node view_results.js open ${htmlFiles[0]}`);
    }
    
  } catch (error) {
    if (error.code === 'ENOENT') {
      console.log("❌ No search results directory found. Run fast_search.js first.");
    } else {
      console.error("❌ Error reading results:", error.message);
    }
  }
}

async function openDashboard(filename = null) {
  const resultsDir = path.join(__dirname, 'search_results');
  
  try {
    const files = await fs.readdir(resultsDir);
    const htmlFiles = files.filter(f => f.endsWith('.html')).sort().reverse();
    
    if (htmlFiles.length === 0) {
      console.log("❌ No dashboard files found.");
      return;
    }
    
    let targetFile;
    if (filename) {
      targetFile = htmlFiles.find(f => f === filename);
      if (!targetFile) {
        console.log(`❌ Dashboard "${filename}" not found.`);
        console.log("Available dashboards:");
        htmlFiles.forEach((f, i) => console.log(`  ${i + 1}. ${f}`));
        return;
      }
    } else {
      targetFile = htmlFiles[0]; // Latest
    }
    
    const filePath = path.join(resultsDir, targetFile);
    console.log(`🌐 Opening dashboard: ${targetFile}`);
    
    // Open in default browser
    const command = process.platform === 'win32' ? 'start' : 
                   process.platform === 'darwin' ? 'open' : 'xdg-open';
    
    exec(`${command} "${filePath}"`, (error) => {
      if (error) {
        console.log(`❌ Could not open browser automatically.`);
        console.log(`📁 Manual path: ${filePath}`);
      } else {
        console.log("✅ Dashboard opened in browser!");
      }
    });
    
  } catch (error) {
    console.error("❌ Error opening dashboard:", error.message);
  }
}

async function showSearchStats() {
  const resultsDir = path.join(__dirname, 'search_results');
  
  try {
    const files = await fs.readdir(resultsDir);
    const jsonFiles = files.filter(f => f.endsWith('.json'));
    
    if (jsonFiles.length === 0) {
      console.log("❌ No search results found.");
      return;
    }
    
    console.log("📈 Search Statistics:");
    console.log("=" .repeat(50));
    
    let totalSearches = 0;
    let totalResults = 0;
    let totalDuration = 0;
    const keywords = {};
    
    for (const file of jsonFiles) {
      const filePath = path.join(resultsDir, file);
      const data = JSON.parse(await fs.readFile(filePath, 'utf8'));
      
      totalSearches++;
      totalResults += data.totalResults;
      totalDuration += data.searchDuration;
      
      keywords[data.keyword] = (keywords[data.keyword] || 0) + 1;
    }
    
    console.log(`🔍 Total searches performed: ${totalSearches}`);
    console.log(`📄 Total results found: ${totalResults}`);
    console.log(`⏱️  Average search time: ${Math.round(totalDuration / totalSearches)}s`);
    console.log(`📊 Average results per search: ${Math.round(totalResults / totalSearches)}`);
    
    console.log("\n🎯 Most searched keywords:");
    Object.entries(keywords)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .forEach(([keyword, count]) => {
        console.log(`  "${keyword}": ${count} searches`);
      });
    
  } catch (error) {
    console.error("❌ Error calculating stats:", error.message);
  }
}

// Command line interface
async function main() {
  const command = process.argv[2];
  const arg = process.argv[3];
  
  switch (command) {
    case 'open':
      await openDashboard(arg);
      break;
    case 'stats':
      await showSearchStats();
      break;
    case 'list':
    default:
      await listSearchResults();
      break;
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  listSearchResults,
  openDashboard,
  showSearchStats
};
