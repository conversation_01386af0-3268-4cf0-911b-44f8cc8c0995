{"timestamp": "2025-06-14T21:34:59.215Z", "searchDuration": 125, "totalAccounts": 1, "processedAccounts": 1, "successfulAccounts": 1, "failedAccounts": 0, "totalResults": 141, "keywords": ["program"], "multiAccount": true, "progressiveUpdate": false, "accounts": [{"email": "<EMAIL>", "success": true, "archives": 2, "results": 141, "error": null, "lastUpdate": "2025-06-14T21:32:53.792Z"}], "results": [{"backupName": "Backup #1", "results": [], "error": null, "message": "No results found in 4 volumes", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "loginCredentials": {"email": "<EMAIL>", "password": "***"}, "accountEmail": "<EMAIL>", "searchKeyword": "program"}, {"backupName": "Backup #2", "results": [{"name": "Program Files", "displayName": "Program Files", "itemType": "Directory", "id": "Backup #2/Data (D:)/Program Files", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "Program Files (x86)", "displayName": "Program Files (x86)", "itemType": "Directory", "id": "Backup #2/Data (D:)/Program Files (x86)", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "ProgramData", "displayName": "ProgramData", "itemType": "Directory", "id": "Backup #2/Data (D:)/ProgramData", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "_ProgramData.lnk", "displayName": "_ProgramData.lnk", "itemType": "Regular", "id": "Backup #2/Data (D:)/Program Files (x86)/MAGIX/Video Sound Cleaning Lab/_ProgramData.lnk", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "boost_program_options.dll", "displayName": "boost_program_options.dll", "itemType": "Regular", "id": "Backup #2/Data (D:)/Program Files/Plex/Plex Media Server/boost_program_options.dll", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "CUnlockVersionProgramVersionExceedsPUG.ini", "displayName": "CUnlockVersionProgramVersionExceedsPUG.ini", "itemType": "Regular", "id": "Backup #2/Data (D:)/Program Files (x86)/Protein/Bitmaps/CUnlockVersionProgramVersionExceedsPUG.ini", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "dvr-program-guide-icon.png", "displayName": "dvr-program-guide-icon.png", "itemType": "Regular", "id": "Backup #2/Data (D:)/Program Files/Plex/Plex Media Server/Resources/Graphics/dvr/dvr-program-guide-icon.png", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program.py", "displayName": "fragment_program.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/ARB/fragment_program.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program.py", "displayName": "fragment_program.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/fragment_program.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program.py", "displayName": "fragment_program.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/ARB/fragment_program.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program.py", "displayName": "fragment_program.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/fragment_program.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program2.py", "displayName": "fragment_program2.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/fragment_program2.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program2.py", "displayName": "fragment_program2.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/fragment_program2.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program4.py", "displayName": "fragment_program4.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/fragment_program4.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program4.py", "displayName": "fragment_program4.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/fragment_program4.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program_option.py", "displayName": "fragment_program_option.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/fragment_program_option.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program_option.py", "displayName": "fragment_program_option.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/fragment_program_option.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program_shadow.py", "displayName": "fragment_program_shadow.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/ARB/fragment_program_shadow.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program_shadow.py", "displayName": "fragment_program_shadow.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/ARB/fragment_program_shadow.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "geometry_program4.py", "displayName": "geometry_program4.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/geometry_program4.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "geometry_program4.py", "displayName": "geometry_program4.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/geometry_program4.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "gpu_program4.py", "displayName": "gpu_program4.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/gpu_program4.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "gpu_program4.py", "displayName": "gpu_program4.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/gpu_program4.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "gpu_program_parameters.py", "displayName": "gpu_program_parameters.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/EXT/gpu_program_parameters.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "gpu_program_parameters.py", "displayName": "gpu_program_parameters.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/EXT/gpu_program_parameters.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "my-program.js", "displayName": "my-program.js", "itemType": "Regular", "id": "Backup #2/Data (D:)/Program Files/Unity/Hub/Editor/2018.4.20f1/Editor/Data/Tools/nodejs/node_modules/npm/node_modules/nopt/examples/my-program.js", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "my-program.js", "displayName": "my-program.js", "itemType": "Regular", "id": "Backup #2/Data (D:)/Program Files/Unity/Hub/Editor/2019.4.14f1/Editor/Data/Tools/nodejs/node_modules/npm/node_modules/nopt/examples/my-program.js", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "my-program.js", "displayName": "my-program.js", "itemType": "Regular", "id": "Backup #2/Data (D:)/Program Files/Unity/Hub/Editor/2019.4.31f1/Editor/Data/Tools/nodejs/node_modules/npm/node_modules/nopt/examples/my-program.js", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "test_program.py", "displayName": "test_program.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/unittest/test/test_program.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program.py", "displayName": "vertex_program.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/ARB/vertex_program.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program.py", "displayName": "vertex_program.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/vertex_program.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program.py", "displayName": "vertex_program.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/ARB/vertex_program.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program.py", "displayName": "vertex_program.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/vertex_program.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program.pyc", "displayName": "vertex_program.pyc", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/ARB/vertex_program.pyc", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program.pyc", "displayName": "vertex_program.pyc", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/ARB/vertex_program.pyc", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program1_1.py", "displayName": "vertex_program1_1.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/vertex_program1_1.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program1_1.py", "displayName": "vertex_program1_1.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/vertex_program1_1.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program2.py", "displayName": "vertex_program2.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/vertex_program2.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program2.py", "displayName": "vertex_program2.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/vertex_program2.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program2_option.py", "displayName": "vertex_program2_option.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/vertex_program2_option.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program2_option.py", "displayName": "vertex_program2_option.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/vertex_program2_option.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program3.py", "displayName": "vertex_program3.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/vertex_program3.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program3.py", "displayName": "vertex_program3.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/vertex_program3.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program4.py", "displayName": "vertex_program4.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/vertex_program4.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program4.py", "displayName": "vertex_program4.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/vertex_program4.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program_evaluators.py", "displayName": "vertex_program_evaluators.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/APPLE/vertex_program_evaluators.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program_evaluators.py", "displayName": "vertex_program_evaluators.py", "itemType": "Regular", "id": "Backup #2/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/APPLE/vertex_program_evaluators.py", "backupFolder": "Backup #2", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}], "error": null, "message": "Found 47 results in 4 volumes", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "loginCredentials": {"email": "<EMAIL>", "password": "***"}, "accountEmail": "<EMAIL>", "searchKeyword": "program"}, {"backupName": "Backup #3", "results": [{"name": "Program Files", "displayName": "Program Files", "itemType": "Directory", "id": "Backup #3/Data (D:)/Program Files", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "Program Files (x86)", "displayName": "Program Files (x86)", "itemType": "Directory", "id": "Backup #3/Data (D:)/Program Files (x86)", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "ProgramData", "displayName": "ProgramData", "itemType": "Directory", "id": "Backup #3/Data (D:)/ProgramData", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "_ProgramData.lnk", "displayName": "_ProgramData.lnk", "itemType": "Regular", "id": "Backup #3/Data (D:)/Program Files (x86)/MAGIX/Video Sound Cleaning Lab/_ProgramData.lnk", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "boost_program_options.dll", "displayName": "boost_program_options.dll", "itemType": "Regular", "id": "Backup #3/Data (D:)/Program Files/Plex/Plex Media Server/boost_program_options.dll", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "CUnlockVersionProgramVersionExceedsPUG.ini", "displayName": "CUnlockVersionProgramVersionExceedsPUG.ini", "itemType": "Regular", "id": "Backup #3/Data (D:)/Program Files (x86)/Protein/Bitmaps/CUnlockVersionProgramVersionExceedsPUG.ini", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "dvr-program-guide-icon.png", "displayName": "dvr-program-guide-icon.png", "itemType": "Regular", "id": "Backup #3/Data (D:)/Program Files/Plex/Plex Media Server/Resources/Graphics/dvr/dvr-program-guide-icon.png", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program.py", "displayName": "fragment_program.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/ARB/fragment_program.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program.py", "displayName": "fragment_program.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/fragment_program.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program.py", "displayName": "fragment_program.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/ARB/fragment_program.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program.py", "displayName": "fragment_program.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/fragment_program.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program2.py", "displayName": "fragment_program2.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/fragment_program2.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program2.py", "displayName": "fragment_program2.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/fragment_program2.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program4.py", "displayName": "fragment_program4.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/fragment_program4.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program4.py", "displayName": "fragment_program4.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/fragment_program4.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program_option.py", "displayName": "fragment_program_option.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/fragment_program_option.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program_option.py", "displayName": "fragment_program_option.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/fragment_program_option.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program_shadow.py", "displayName": "fragment_program_shadow.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/ARB/fragment_program_shadow.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program_shadow.py", "displayName": "fragment_program_shadow.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/ARB/fragment_program_shadow.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "geometry_program4.py", "displayName": "geometry_program4.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/geometry_program4.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "geometry_program4.py", "displayName": "geometry_program4.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/geometry_program4.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "gpu_program4.py", "displayName": "gpu_program4.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/gpu_program4.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "gpu_program4.py", "displayName": "gpu_program4.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/gpu_program4.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "gpu_program_parameters.py", "displayName": "gpu_program_parameters.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/EXT/gpu_program_parameters.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "gpu_program_parameters.py", "displayName": "gpu_program_parameters.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/EXT/gpu_program_parameters.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "my-program.js", "displayName": "my-program.js", "itemType": "Regular", "id": "Backup #3/Data (D:)/Program Files/Unity/Hub/Editor/2018.4.20f1/Editor/Data/Tools/nodejs/node_modules/npm/node_modules/nopt/examples/my-program.js", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "my-program.js", "displayName": "my-program.js", "itemType": "Regular", "id": "Backup #3/Data (D:)/Program Files/Unity/Hub/Editor/2019.4.14f1/Editor/Data/Tools/nodejs/node_modules/npm/node_modules/nopt/examples/my-program.js", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "my-program.js", "displayName": "my-program.js", "itemType": "Regular", "id": "Backup #3/Data (D:)/Program Files/Unity/Hub/Editor/2019.4.31f1/Editor/Data/Tools/nodejs/node_modules/npm/node_modules/nopt/examples/my-program.js", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "test_program.py", "displayName": "test_program.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/unittest/test/test_program.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program.py", "displayName": "vertex_program.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/ARB/vertex_program.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program.py", "displayName": "vertex_program.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/vertex_program.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program.py", "displayName": "vertex_program.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/ARB/vertex_program.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program.py", "displayName": "vertex_program.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/vertex_program.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program.pyc", "displayName": "vertex_program.pyc", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/ARB/vertex_program.pyc", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program.pyc", "displayName": "vertex_program.pyc", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/ARB/vertex_program.pyc", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program1_1.py", "displayName": "vertex_program1_1.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/vertex_program1_1.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program1_1.py", "displayName": "vertex_program1_1.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/vertex_program1_1.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program2.py", "displayName": "vertex_program2.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/vertex_program2.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program2.py", "displayName": "vertex_program2.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/vertex_program2.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program2_option.py", "displayName": "vertex_program2_option.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/vertex_program2_option.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program2_option.py", "displayName": "vertex_program2_option.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/vertex_program2_option.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program3.py", "displayName": "vertex_program3.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/vertex_program3.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program3.py", "displayName": "vertex_program3.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/vertex_program3.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program4.py", "displayName": "vertex_program4.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/vertex_program4.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program4.py", "displayName": "vertex_program4.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/vertex_program4.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program_evaluators.py", "displayName": "vertex_program_evaluators.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/APPLE/vertex_program_evaluators.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program_evaluators.py", "displayName": "vertex_program_evaluators.py", "itemType": "Regular", "id": "Backup #3/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/APPLE/vertex_program_evaluators.py", "backupFolder": "Backup #3", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}], "error": null, "message": "Found 47 results in 4 volumes", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "loginCredentials": {"email": "<EMAIL>", "password": "***"}, "accountEmail": "<EMAIL>", "searchKeyword": "program"}, {"backupName": "Backup #4", "results": [{"name": "Program Files", "displayName": "Program Files", "itemType": "Directory", "id": "Backup #4/Data (D:)/Program Files", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "Program Files (x86)", "displayName": "Program Files (x86)", "itemType": "Directory", "id": "Backup #4/Data (D:)/Program Files (x86)", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "ProgramData", "displayName": "ProgramData", "itemType": "Directory", "id": "Backup #4/Data (D:)/ProgramData", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "_ProgramData.lnk", "displayName": "_ProgramData.lnk", "itemType": "Regular", "id": "Backup #4/Data (D:)/Program Files (x86)/MAGIX/Video Sound Cleaning Lab/_ProgramData.lnk", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "boost_program_options.dll", "displayName": "boost_program_options.dll", "itemType": "Regular", "id": "Backup #4/Data (D:)/Program Files/Plex/Plex Media Server/boost_program_options.dll", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "CUnlockVersionProgramVersionExceedsPUG.ini", "displayName": "CUnlockVersionProgramVersionExceedsPUG.ini", "itemType": "Regular", "id": "Backup #4/Data (D:)/Program Files (x86)/Protein/Bitmaps/CUnlockVersionProgramVersionExceedsPUG.ini", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "dvr-program-guide-icon.png", "displayName": "dvr-program-guide-icon.png", "itemType": "Regular", "id": "Backup #4/Data (D:)/Program Files/Plex/Plex Media Server/Resources/Graphics/dvr/dvr-program-guide-icon.png", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program.py", "displayName": "fragment_program.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/ARB/fragment_program.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program.py", "displayName": "fragment_program.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/fragment_program.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program.py", "displayName": "fragment_program.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/ARB/fragment_program.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program.py", "displayName": "fragment_program.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/fragment_program.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program2.py", "displayName": "fragment_program2.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/fragment_program2.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program2.py", "displayName": "fragment_program2.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/fragment_program2.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program4.py", "displayName": "fragment_program4.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/fragment_program4.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program4.py", "displayName": "fragment_program4.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/fragment_program4.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program_option.py", "displayName": "fragment_program_option.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/fragment_program_option.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program_option.py", "displayName": "fragment_program_option.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/fragment_program_option.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program_shadow.py", "displayName": "fragment_program_shadow.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/ARB/fragment_program_shadow.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "fragment_program_shadow.py", "displayName": "fragment_program_shadow.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/ARB/fragment_program_shadow.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "geometry_program4.py", "displayName": "geometry_program4.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/geometry_program4.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "geometry_program4.py", "displayName": "geometry_program4.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/geometry_program4.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "gpu_program4.py", "displayName": "gpu_program4.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/gpu_program4.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "gpu_program4.py", "displayName": "gpu_program4.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/gpu_program4.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "gpu_program_parameters.py", "displayName": "gpu_program_parameters.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/EXT/gpu_program_parameters.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "gpu_program_parameters.py", "displayName": "gpu_program_parameters.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/EXT/gpu_program_parameters.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "my-program.js", "displayName": "my-program.js", "itemType": "Regular", "id": "Backup #4/Data (D:)/Program Files/Unity/Hub/Editor/2018.4.20f1/Editor/Data/Tools/nodejs/node_modules/npm/node_modules/nopt/examples/my-program.js", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "my-program.js", "displayName": "my-program.js", "itemType": "Regular", "id": "Backup #4/Data (D:)/Program Files/Unity/Hub/Editor/2019.4.14f1/Editor/Data/Tools/nodejs/node_modules/npm/node_modules/nopt/examples/my-program.js", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "my-program.js", "displayName": "my-program.js", "itemType": "Regular", "id": "Backup #4/Data (D:)/Program Files/Unity/Hub/Editor/2019.4.31f1/Editor/Data/Tools/nodejs/node_modules/npm/node_modules/nopt/examples/my-program.js", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "test_program.py", "displayName": "test_program.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/unittest/test/test_program.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program.py", "displayName": "vertex_program.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/ARB/vertex_program.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program.py", "displayName": "vertex_program.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/vertex_program.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program.py", "displayName": "vertex_program.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/ARB/vertex_program.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program.py", "displayName": "vertex_program.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/vertex_program.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program.pyc", "displayName": "vertex_program.pyc", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/ARB/vertex_program.pyc", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program.pyc", "displayName": "vertex_program.pyc", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/ARB/vertex_program.pyc", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program1_1.py", "displayName": "vertex_program1_1.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/vertex_program1_1.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program1_1.py", "displayName": "vertex_program1_1.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/vertex_program1_1.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program2.py", "displayName": "vertex_program2.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/vertex_program2.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program2.py", "displayName": "vertex_program2.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/vertex_program2.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program2_option.py", "displayName": "vertex_program2_option.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/vertex_program2_option.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program2_option.py", "displayName": "vertex_program2_option.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/vertex_program2_option.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program3.py", "displayName": "vertex_program3.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/vertex_program3.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program3.py", "displayName": "vertex_program3.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/vertex_program3.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program4.py", "displayName": "vertex_program4.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/NV/vertex_program4.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program4.py", "displayName": "vertex_program4.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/NV/vertex_program4.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program_evaluators.py", "displayName": "vertex_program_evaluators.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/GL/APPLE/vertex_program_evaluators.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}, {"name": "vertex_program_evaluators.py", "displayName": "vertex_program_evaluators.py", "itemType": "Regular", "id": "Backup #4/Data (D:)/Users/<USER>/AppData/Roaming/Creality Slicer/Creality Slicer/python/Lib/OpenGL/raw/GL/APPLE/vertex_program_evaluators.py", "backupFolder": "Backup #4", "archiveName": "LAPTOP-24FF84HR", "accountEmail": "<EMAIL>", "searchMetadata": {"email": "<EMAIL>", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "searchTimestamp": "2025-06-14T21:34:57.474Z", "keyword": "program"}}], "error": null, "message": "Found 47 results in 4 volumes", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "loginCredentials": {"email": "<EMAIL>", "password": "***"}, "accountEmail": "<EMAIL>", "searchKeyword": "program"}, {"backupName": "Backup #5", "results": [], "error": null, "message": "No results found in 4 volumes", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "loginCredentials": {"email": "<EMAIL>", "password": "***"}, "accountEmail": "<EMAIL>", "searchKeyword": "program"}, {"backupName": "Backup #6", "results": [], "error": null, "message": "No volumes found in backup structure and direct search failed", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "loginCredentials": {"email": "<EMAIL>", "password": "***"}, "accountEmail": "<EMAIL>", "searchKeyword": "program"}, {"backupName": "Backup #7", "results": [], "error": null, "message": "No volumes found in backup structure and direct search failed", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "loginCredentials": {"email": "<EMAIL>", "password": "***"}, "accountEmail": "<EMAIL>", "searchKeyword": "program"}, {"backupName": "Backup #8", "results": [], "error": null, "message": "No volumes found in backup structure and direct search failed", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "loginCredentials": {"email": "<EMAIL>", "password": "***"}, "accountEmail": "<EMAIL>", "searchKeyword": "program"}, {"backupName": "Backup #9", "results": [], "error": null, "message": "No volumes found in backup structure and direct search failed", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "loginCredentials": {"email": "<EMAIL>", "password": "***"}, "accountEmail": "<EMAIL>", "searchKeyword": "program"}, {"backupName": "Backup #10", "results": [], "error": null, "message": "No volumes found in backup structure and direct search failed", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "loginCredentials": {"email": "<EMAIL>", "password": "***"}, "accountEmail": "<EMAIL>", "searchKeyword": "program"}, {"backupName": "Backup #11", "results": [], "error": null, "message": "No volumes found in backup structure and direct search failed", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "loginCredentials": {"email": "<EMAIL>", "password": "***"}, "accountEmail": "<EMAIL>", "searchKeyword": "program"}, {"backupName": "Backup #12", "results": [], "error": null, "message": "No volumes found in backup structure and direct search failed", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "loginCredentials": {"email": "<EMAIL>", "password": "***"}, "accountEmail": "<EMAIL>", "searchKeyword": "program"}, {"backupName": "Backup #13", "results": [], "error": null, "message": "No volumes found in backup structure and direct search failed", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "loginCredentials": {"email": "<EMAIL>", "password": "***"}, "accountEmail": "<EMAIL>", "searchKeyword": "program"}, {"backupName": "Backup #14", "results": [], "error": null, "message": "No volumes found in backup structure and direct search failed", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "loginCredentials": {"email": "<EMAIL>", "password": "***"}, "accountEmail": "<EMAIL>", "searchKeyword": "program"}, {"backupName": "Backup #15", "results": [], "error": null, "message": "No volumes found in backup structure and direct search failed", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "loginCredentials": {"email": "<EMAIL>", "password": "***"}, "accountEmail": "<EMAIL>", "searchKeyword": "program"}, {"backupName": "Backup #16", "results": [], "error": null, "message": "No volumes found in backup structure and direct search failed", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "loginCredentials": {"email": "<EMAIL>", "password": "***"}, "accountEmail": "<EMAIL>", "searchKeyword": "program"}, {"backupName": "Backup #17", "results": [], "error": null, "message": "No volumes found in backup structure and direct search failed", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "loginCredentials": {"email": "<EMAIL>", "password": "***"}, "accountEmail": "<EMAIL>", "searchKeyword": "program"}, {"backupName": "Backup #18", "results": [], "error": null, "message": "No volumes found in backup structure and direct search failed", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "loginCredentials": {"email": "<EMAIL>", "password": "***"}, "accountEmail": "<EMAIL>", "searchKeyword": "program"}, {"backupName": "Backup #19", "results": [], "error": null, "message": "No volumes found in backup structure and direct search failed", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "loginCredentials": {"email": "<EMAIL>", "password": "***"}, "accountEmail": "<EMAIL>", "searchKeyword": "program"}, {"backupName": "Backup #20", "results": [], "error": null, "message": "No volumes found in backup structure and direct search failed", "archiveId": "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR", "archiveName": "LAPTOP-24FF84HR", "datacenter": "113", "loginCredentials": {"email": "<EMAIL>", "password": "***"}, "accountEmail": "<EMAIL>", "searchKeyword": "program"}]}