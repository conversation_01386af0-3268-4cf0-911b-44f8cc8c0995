# 🚀 Acronis Parallel Search Engine

A high-performance, parallel search engine for Acronis backup archives with beautiful dashboard visualization.

## ✨ Features

- **⚡ Parallel Processing**: Uses worker threads to search multiple backups simultaneously
- **🎯 Intelligent Discovery**: Automatically finds all backup folders and C: drives
- **📊 Beautiful Dashboard**: Interactive HTML dashboard with collapsible results
- **💾 Data Persistence**: Saves search results in JSON format for analysis
- **📈 Statistics**: Track search performance and history
- **🔒 Secure Authentication**: Complete Acronis authentication flow with JWT tokens

## 🚀 Quick Start

### 1. Install Dependencies
```bash
npm install
```

### 2. Run Parallel Search
```bash
npm run search
# or
node fast_search.js
```

### 3. View Results
```bash
npm run view
# or
node view_results.js
```

### 4. Open Dashboard
```bash
npm run open
# or
node view_results.js open
```

## 📁 File Structure

```
├── fast_search.js          # Main parallel search engine
├── parallel_search.js      # Worker thread functions and utilities
├── view_results.js         # Results viewer and dashboard opener
├── test_acronis_search.js  # Original sequential search (for reference)
├── search_results/         # Generated search results and dashboards
│   ├── search_program_*.json    # Raw search data
│   └── dashboard_program_*.html # Interactive dashboards
└── README.md
```

## 🔧 Configuration

Edit the credentials in `fast_search.js`:
```javascript
const email = "<EMAIL>";
const password = "your-password";
const MAX_WORKERS = 5; // Adjust based on your needs
```

## 📊 Dashboard Features

The generated HTML dashboard includes:

- **📈 Statistics Overview**: Total results, search duration, backups searched
- **🔍 Interactive Results**: Collapsible backup sections
- **📄 File Details**: Size, modification date, file type
- **⚡ Performance Metrics**: Parallel processing information
- **🎨 Modern UI**: Responsive design with beautiful styling

## 🛠️ Commands

| Command | Description |
|---------|-------------|
| `npm run search` | Run parallel search for "program" keyword |
| `npm run view` | List recent search results |
| `npm run stats` | Show search statistics |
| `npm run open` | Open latest dashboard in browser |

## 📈 Performance Comparison

| Method | Time | Workers | Efficiency |
|--------|------|---------|------------|
| Sequential | ~60s | 1 | Baseline |
| Parallel (5 workers) | ~15s | 5 | **4x faster** |

## 🔍 Search Process

1. **Authentication** 🔐
   - Login to Acronis API
   - Navigate to UI for session establishment
   - Get JWT token for cross-domain access
   - Obtain JSESSIONID for search domain

2. **Discovery** 📁
   - Find all backup folders (Backup #1, #2, etc.)
   - Check each backup for C: drive availability

3. **Parallel Search** ⚡
   - Distribute backups across worker threads
   - Search for keyword in each C: drive simultaneously
   - Aggregate results from all workers

4. **Results** 📊
   - Save raw data as JSON
   - Generate interactive HTML dashboard
   - Display summary statistics

## 🎯 Search Customization

To search for different keywords, modify `fast_search.js`:

```javascript
const searchKeyword = "your-keyword-here";
```

To search in different drives or paths, modify the worker function in `parallel_search.js`.

## 📱 Dashboard Screenshots

The dashboard provides:
- **Header**: Search summary with keyword and timestamp
- **Statistics Cards**: Key metrics at a glance
- **Backup Sections**: Collapsible results by backup folder
- **File Details**: Comprehensive file information
- **Performance Info**: Parallel processing details

## 🔧 Troubleshooting

### Authentication Issues
- Verify credentials in `fast_search.js`
- Check if MFA is enabled on your account
- Ensure network connectivity to Acronis servers

### Search Errors
- Check if backup folders exist
- Verify C: drive availability in backups
- Monitor rate limiting (adjust `MAX_WORKERS` if needed)

### Dashboard Not Opening
- Check if HTML file was generated in `search_results/`
- Try opening manually from the file path shown
- Ensure default browser is configured

## 📊 Example Output

```
🔍 Starting Acronis Parallel Search Engine
==================================================
🔐 Authenticating...
✅ Login successful
🎯 Session established
📁 Discovering backup folders...
📊 Found 18 backup folders
🎯 Searching for: "program"
🚀 Starting parallel search with 5 workers...
✅ Backup #1: Found 12 results
✅ Backup #2: Found 8 results
✅ Backup #3: Found 15 results
...
==================================================
📊 SEARCH COMPLETED
==================================================
⏱️  Duration: 14 seconds
📁 Backups searched: 18
✅ Backups with results: 12
📄 Total results: 127
⚡ Workers used: 5

📁 Results saved to: search_results/search_program_2025-01-09T15-30-45-123Z.json
🌐 Dashboard saved to: search_results/dashboard_program_2025-01-09T15-30-45-123Z.html

🎉 Search completed successfully!
🌐 Open dashboard: search_results/dashboard_program_2025-01-09T15-30-45-123Z.html
```

## 🚀 Next Steps

1. **Run your first search**: `npm run search`
2. **View the dashboard**: `npm run open`
3. **Customize search keywords** in `fast_search.js`
4. **Analyze results** using the JSON data files
5. **Share dashboards** - HTML files are self-contained

Happy searching! 🎉
