#!/usr/bin/env node

/**
 * Test script to analyze and demonstrate worker exit code issues
 * This script helps identify common causes of worker failures
 */

const fs = require('fs').promises;
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');

// Simulate different types of worker failures
const FAILURE_TYPES = {
  TIMEOUT: 'timeout',
  MEMORY: 'memory',
  NETWORK: 'network',
  AUTHENTICATION: 'auth',
  UNHANDLED_REJECTION: 'unhandled'
};

async function testWorkerStability() {
  console.log('🧪 Testing Worker Stability and Exit Code Analysis');
  console.log('==================================================');
  
  const results = {
    totalTests: 0,
    successful: 0,
    failed: 0,
    exitCodes: {},
    failureTypes: {}
  };

  // Test 1: Normal worker operation
  console.log('\n1. Testing normal worker operation...');
  try {
    const result = await runWorkerTest('normal');
    console.log('✅ Normal worker completed successfully');
    results.successful++;
  } catch (error) {
    console.log(`❌ Normal worker failed: ${error.message}`);
    results.failed++;
    recordFailure(results, error);
  }
  results.totalTests++;

  // Test 2: Timeout scenario
  console.log('\n2. Testing worker timeout scenario...');
  try {
    const result = await runWorkerTest('timeout', 5000); // 5 second timeout
    console.log('✅ Timeout test completed');
    results.successful++;
  } catch (error) {
    console.log(`❌ Timeout test failed: ${error.message}`);
    results.failed++;
    recordFailure(results, error);
  }
  results.totalTests++;

  // Test 3: Memory pressure
  console.log('\n3. Testing memory pressure scenario...');
  try {
    const result = await runWorkerTest('memory');
    console.log('✅ Memory test completed');
    results.successful++;
  } catch (error) {
    console.log(`❌ Memory test failed: ${error.message}`);
    results.failed++;
    recordFailure(results, error);
  }
  results.totalTests++;

  // Test 4: Network failure simulation
  console.log('\n4. Testing network failure scenario...');
  try {
    const result = await runWorkerTest('network');
    console.log('✅ Network test completed');
    results.successful++;
  } catch (error) {
    console.log(`❌ Network test failed: ${error.message}`);
    results.failed++;
    recordFailure(results, error);
  }
  results.totalTests++;

  // Test 5: Unhandled promise rejection
  console.log('\n5. Testing unhandled promise rejection...');
  try {
    const result = await runWorkerTest('unhandled');
    console.log('✅ Unhandled rejection test completed');
    results.successful++;
  } catch (error) {
    console.log(`❌ Unhandled rejection test failed: ${error.message}`);
    results.failed++;
    recordFailure(results, error);
  }
  results.totalTests++;

  // Display results
  console.log('\n📊 Worker Stability Test Results:');
  console.log('================================');
  console.log(`Total tests: ${results.totalTests}`);
  console.log(`Successful: ${results.successful}`);
  console.log(`Failed: ${results.failed}`);
  console.log(`Success rate: ${((results.successful / results.totalTests) * 100).toFixed(1)}%`);

  if (Object.keys(results.exitCodes).length > 0) {
    console.log('\n🚨 Exit codes encountered:');
    Object.entries(results.exitCodes).forEach(([code, count]) => {
      console.log(`   Exit code ${code}: ${count} times`);
    });
  }

  if (Object.keys(results.failureTypes).length > 0) {
    console.log('\n🔍 Failure types:');
    Object.entries(results.failureTypes).forEach(([type, count]) => {
      console.log(`   ${type}: ${count} times`);
    });
  }

  // Recommendations
  console.log('\n💡 Recommendations to reduce worker exit codes:');
  console.log('1. Reduce timeout values (current: 15min → recommended: 3-5min)');
  console.log('2. Limit concurrent workers (current: 5 → recommended: 2-3)');
  console.log('3. Add memory limits to worker processes');
  console.log('4. Implement exponential backoff for retries');
  console.log('5. Add graceful shutdown handling');
  console.log('6. Monitor worker memory usage');
  console.log('7. Implement circuit breaker pattern for network failures');

  return results;
}

function runWorkerTest(testType, timeout = 10000) {
  return new Promise((resolve, reject) => {
    const worker = new Worker(__filename, {
      workerData: { testType }
    });

    const timer = setTimeout(() => {
      worker.terminate();
      reject(new Error(`Worker timeout after ${timeout}ms`));
    }, timeout);

    worker.on('message', (message) => {
      clearTimeout(timer);
      if (message.success) {
        resolve(message.data);
      } else {
        reject(new Error(message.error));
      }
    });

    worker.on('error', (error) => {
      clearTimeout(timer);
      reject(new Error(`Worker error: ${error.message}`));
    });

    worker.on('exit', (code) => {
      clearTimeout(timer);
      if (code !== 0) {
        reject(new Error(`Worker exit code: ${code}`));
      }
    });
  });
}

function recordFailure(results, error) {
  // Extract exit code if present
  const exitCodeMatch = error.message.match(/exit code:?\s*(\d+)/i);
  if (exitCodeMatch) {
    const code = exitCodeMatch[1];
    results.exitCodes[code] = (results.exitCodes[code] || 0) + 1;
  }

  // Categorize failure type
  const message = error.message.toLowerCase();
  if (message.includes('timeout')) {
    results.failureTypes['timeout'] = (results.failureTypes['timeout'] || 0) + 1;
  } else if (message.includes('memory')) {
    results.failureTypes['memory'] = (results.failureTypes['memory'] || 0) + 1;
  } else if (message.includes('network') || message.includes('connection')) {
    results.failureTypes['network'] = (results.failureTypes['network'] || 0) + 1;
  } else if (message.includes('auth')) {
    results.failureTypes['authentication'] = (results.failureTypes['authentication'] || 0) + 1;
  } else {
    results.failureTypes['other'] = (results.failureTypes['other'] || 0) + 1;
  }
}

// Worker thread code
if (!isMainThread) {
  const { testType } = workerData;

  try {
    switch (testType) {
      case 'normal':
        // Simulate normal operation
        setTimeout(() => {
          parentPort.postMessage({ 
            success: true, 
            data: { message: 'Normal operation completed' }
          });
        }, 1000);
        break;

      case 'timeout':
        // Simulate long-running operation that might timeout
        setTimeout(() => {
          parentPort.postMessage({ 
            success: true, 
            data: { message: 'Long operation completed' }
          });
        }, 8000); // 8 seconds - might timeout with 5 second limit
        break;

      case 'memory':
        // Simulate memory pressure (controlled)
        const largeArray = new Array(1000000).fill('test data');
        setTimeout(() => {
          parentPort.postMessage({ 
            success: true, 
            data: { message: `Memory test completed with ${largeArray.length} items` }
          });
        }, 2000);
        break;

      case 'network':
        // Simulate network failure
        setTimeout(() => {
          parentPort.postMessage({ 
            success: false, 
            error: 'Network connection failed'
          });
        }, 1500);
        break;

      case 'unhandled':
        // Simulate unhandled promise rejection
        setTimeout(() => {
          Promise.reject(new Error('Unhandled promise rejection'));
          parentPort.postMessage({ 
            success: true, 
            data: { message: 'Unhandled rejection test' }
          });
        }, 1000);
        break;

      default:
        parentPort.postMessage({ 
          success: false, 
          error: `Unknown test type: ${testType}` 
        });
    }
  } catch (error) {
    parentPort.postMessage({ 
      success: false, 
      error: error.message 
    });
  }
}

if (require.main === module) {
  testWorkerStability().then(results => {
    console.log('\n🎯 Test completed. Check the recommendations above to improve worker stability.');
    process.exit(results.failed > 0 ? 1 : 0);
  }).catch(error => {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  });
}

module.exports = { testWorkerStability };
