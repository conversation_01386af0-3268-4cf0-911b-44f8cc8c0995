#!/usr/bin/env node

/**
 * Debug script to understand backup folder and volume structure
 * This helps identify why we're getting mount points instead of real volume IDs
 */

const fs = require('fs').promises;

async function analyzeSearchResults() {
    console.log('🔍 Analyzing Search Results Structure');
    console.log('====================================');

    try {
        const resultsData = await fs.readFile('search_results/RESULT.json', 'utf8');
        const results = JSON.parse(resultsData);

        console.log(`📊 Total results: ${results.totalResults}`);
        console.log(`📦 Total accounts: ${results.accounts.length}`);

        // Analyze the first few results to understand structure
        if (results.accounts.length > 0) {
            const firstAccount = results.accounts[0];
            console.log(`\n👤 First Account: ${firstAccount.email}`);
            console.log(`📁 Archives: ${firstAccount.results.length}`);

            if (firstAccount.results.length > 0) {
                const firstArchive = firstAccount.results[0];
                console.log(`\n📦 First Archive: ${firstArchive.backupName}`);
                console.log(`🔍 Results count: ${firstArchive.results.length}`);

                if (firstArchive.results.length > 0) {
                    console.log('\n🧪 Sample Results Analysis:');
                    
                    // Analyze first 10 results
                    const sampleResults = firstArchive.results.slice(0, 10);
                    
                    sampleResults.forEach((result, index) => {
                        console.log(`\n${index + 1}. Result Analysis:`);
                        console.log(`   Name: "${result.name}"`);
                        console.log(`   Display Name: "${result.displayName}"`);
                        console.log(`   ID: "${result.id}"`);
                        console.log(`   Item Type: "${result.itemType}"`);
                        console.log(`   Backup Folder: "${result.backupFolder}"`);
                        
                        // Check if name, displayName, and id are identical
                        const identical = result.name === result.displayName && result.displayName === result.id;
                        console.log(`   🔍 Name/DisplayName/ID identical: ${identical ? '❌ YES (Mount Point)' : '✅ NO (Real Volume)'}`);
                        
                        if (result.searchMetadata) {
                            console.log(`   Archive ID: "${result.searchMetadata.archiveId}"`);
                            console.log(`   Datacenter: "${result.searchMetadata.datacenter}"`);
                        }
                    });

                    // Analyze patterns
                    console.log('\n📈 Pattern Analysis:');
                    
                    const itemTypes = {};
                    const mountPointCount = sampleResults.filter(r => r.itemType === 'MountPoint').length;
                    const identicalCount = sampleResults.filter(r => r.name === r.displayName && r.displayName === r.id).length;
                    
                    sampleResults.forEach(result => {
                        itemTypes[result.itemType] = (itemTypes[result.itemType] || 0) + 1;
                    });

                    console.log(`   Mount Points: ${mountPointCount}/${sampleResults.length}`);
                    console.log(`   Identical name/displayName/id: ${identicalCount}/${sampleResults.length}`);
                    console.log(`   Item Types:`, itemTypes);

                    // Check for backup folder patterns
                    const backupFolders = [...new Set(sampleResults.map(r => r.backupFolder))];
                    console.log(`   Unique Backup Folders: ${backupFolders.join(', ')}`);
                }
            }
        }

        // Analyze the problem
        console.log('\n🚨 Problem Analysis:');
        console.log('===================');
        
        console.log('The issue is that we\'re getting mount point results instead of file-level results.');
        console.log('This happens because:');
        console.log('1. ❌ backup.id is just the backup folder name (e.g., "Backup #1")');
        console.log('2. ❌ We\'re not discovering actual volumes within backup folders');
        console.log('3. ❌ Direct search fallback uses backup folder name as volumePath');
        console.log('4. ❌ This returns mount points instead of diving into directory structure');

        console.log('\n💡 Solution:');
        console.log('============');
        console.log('We need to:');
        console.log('1. ✅ Discover actual volume IDs within each backup folder');
        console.log('2. ✅ Use those volume IDs (e.g., "Data (D:)", "Windows-SSD (C:)") as volumePath');
        console.log('3. ✅ Search within each discovered volume separately');
        console.log('4. ✅ This will return actual files instead of mount points');

    } catch (error) {
        console.error('❌ Error analyzing results:', error.message);
    }
}

async function demonstrateCorrectVolumeUsage() {
    console.log('\n🎯 Correct Volume Path Usage Example:');
    console.log('====================================');
    
    console.log('❌ Current (Wrong) - Using backup folder name:');
    console.log('   volumePath: "Backup #1"');
    console.log('   Result: Mount points like "Data (D:)" with identical name/displayName/id');
    
    console.log('\n✅ Correct - Using actual volume IDs:');
    console.log('   Step 1: Discover volumes in "Backup #1"');
    console.log('   Found volumes: ["Data (D:)", "Windows-SSD (C:)", "SYSTEM_DRV", "WINRE_DRV"]');
    console.log('   Step 2: Search each volume separately:');
    console.log('     volumePath: "Data (D:)"     → Returns files in D: drive');
    console.log('     volumePath: "Windows-SSD (C:)" → Returns files in C: drive');
    console.log('     volumePath: "SYSTEM_DRV"   → Returns files in system partition');
    console.log('     volumePath: "WINRE_DRV"    → Returns files in recovery partition');
    
    console.log('\n🔍 Expected Result Structure:');
    console.log('   name: "Documents/project.docx"');
    console.log('   displayName: "project.docx"');
    console.log('   id: "/Users/<USER>/Documents/project.docx"');
    console.log('   itemType: "File"');
    console.log('   backupFolder: "Backup #1"');
}

async function suggestImplementationFix() {
    console.log('\n🛠️  Implementation Fix Needed:');
    console.log('=============================');
    
    console.log('The findVolumesRecursively function should be working, but it\'s not finding volumes.');
    console.log('This suggests either:');
    console.log('1. 🔍 The volume discovery logic needs debugging');
    console.log('2. 🔍 The API response structure has changed');
    console.log('3. 🔍 We need to look deeper in the backup folder structure');
    
    console.log('\nNext steps:');
    console.log('1. 🔧 Add more detailed logging to findVolumesRecursively');
    console.log('2. 🔧 Check what items are actually returned from backup folders');
    console.log('3. 🔧 Verify the volume detection criteria');
    console.log('4. 🔧 Test with a single backup folder to see the structure');
}

if (require.main === module) {
    analyzeSearchResults()
        .then(() => demonstrateCorrectVolumeUsage())
        .then(() => suggestImplementationFix())
        .then(() => {
            console.log('\n🎯 Summary:');
            console.log('The numerical sorting is working perfectly!');
            console.log('The volume path issue is that we need to discover and use actual volume IDs');
            console.log('instead of backup folder names for the volumePath parameter.');
            console.log('\nThis will give us file-level results instead of mount points.');
        })
        .catch(error => {
            console.error('❌ Analysis failed:', error.message);
            process.exit(1);
        });
}

module.exports = { analyzeSearchResults };
