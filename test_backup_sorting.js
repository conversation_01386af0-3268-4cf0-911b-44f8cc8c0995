#!/usr/bin/env node

/**
 * Test script to verify numerical sorting of backup folders
 */

function testBackupSorting() {
    console.log('🧪 Testing Backup Folder Numerical Sorting');
    console.log('==========================================');

    // Test data simulating backup folders from Acronis API
    const testBackupFolders = [
        { name: "Backup #10", id: "backup-10", folderItem: true },
        { name: "Backup #2", id: "backup-2", folderItem: true },
        { name: "Backup #1", id: "backup-1", folderItem: true },
        { name: "Backup #15", id: "backup-15", folderItem: true },
        { name: "Backup #3", id: "backup-3", folderItem: true },
        { name: "Backup #11", id: "backup-11", folderItem: true },
        { name: "Backup #5", id: "backup-5", folderItem: true },
        { name: "Backup #20", id: "backup-20", folderItem: true },
        { name: "Backup #7", id: "backup-7", folderItem: true }
    ];

    console.log('\n📋 Original order:');
    testBackupFolders.forEach((backup, index) => {
        console.log(`   ${index + 1}. ${backup.name}`);
    });

    // Filter for backup folders (simulating the filter in the actual code)
    let backupFolders = testBackupFolders.filter(item =>
        item.folderItem && item.name.startsWith("Backup #")
    );

    console.log('\n🔍 After filtering for "Backup #":');
    backupFolders.forEach((backup, index) => {
        console.log(`   ${index + 1}. ${backup.name}`);
    });

    // Apply numerical sorting
    backupFolders.sort((a, b) => {
        const na = parseInt(a.name.replace('Backup #', ''), 10);
        const nb = parseInt(b.name.replace('Backup #', ''), 10);
        return na - nb;
    });

    console.log('\n✅ After numerical sorting:');
    backupFolders.forEach((backup, index) => {
        console.log(`   ${index + 1}. ${backup.name}`);
    });

    // Verify the sorting is correct
    const expectedOrder = [
        "Backup #1", "Backup #2", "Backup #3", "Backup #5", "Backup #7",
        "Backup #10", "Backup #11", "Backup #15", "Backup #20"
    ];

    const actualOrder = backupFolders.map(b => b.name);
    const isCorrect = JSON.stringify(actualOrder) === JSON.stringify(expectedOrder);

    console.log('\n📊 Sorting Verification:');
    console.log(`Expected: ${expectedOrder.join(', ')}`);
    console.log(`Actual:   ${actualOrder.join(', ')}`);
    console.log(`Result:   ${isCorrect ? '✅ CORRECT' : '❌ INCORRECT'}`);

    // Test edge cases
    console.log('\n🧪 Testing Edge Cases:');
    
    // Test with mixed content
    const mixedContent = [
        { name: "Backup #100", id: "backup-100", folderItem: true },
        { name: "Some Other Folder", id: "other", folderItem: true },
        { name: "Backup #9", id: "backup-9", folderItem: true },
        { name: "Data Drive", id: "drive", folderItem: false },
        { name: "Backup #1", id: "backup-1", folderItem: true }
    ];

    let filteredMixed = mixedContent.filter(item =>
        item.folderItem && item.name.startsWith("Backup #")
    );

    filteredMixed.sort((a, b) => {
        const na = parseInt(a.name.replace('Backup #', ''), 10);
        const nb = parseInt(b.name.replace('Backup #', ''), 10);
        return na - nb;
    });

    console.log('Mixed content after filtering and sorting:');
    filteredMixed.forEach((backup, index) => {
        console.log(`   ${index + 1}. ${backup.name}`);
    });

    // Test volume path usage
    console.log('\n🔍 Testing Volume Path Usage:');
    const testBackup = { 
        name: "Backup #5", 
        id: "113//99DBC6FF-1353-4699-820F-3A372B5AC282/LAPTOP-24FF84HR/Backup #5",
        folderItem: true 
    };

    const searchPath = testBackup.id || testBackup.name;
    console.log(`Backup name: ${testBackup.name}`);
    console.log(`Backup ID: ${testBackup.id}`);
    console.log(`Search path (real volume path): ${searchPath}`);
    console.log(`✅ Using real volume path instead of just backup name`);

    return isCorrect;
}

function testDashboardSorting() {
    console.log('\n🌐 Testing Dashboard Sorting Logic');
    console.log('==================================');

    // Simulate the dashboard sorting logic
    const allResults = [
        { backupFolder: "Backup #10", name: "file1.txt" },
        { backupFolder: "Backup #2", name: "file2.txt" },
        { backupFolder: "Backup #1", name: "file3.txt" },
        { backupFolder: "Backup #15", name: "file4.txt" },
        { backupFolder: "Backup #3", name: "file5.txt" }
    ];

    // Get unique backup folders and sort them numerically (dashboard logic)
    const backups = [...new Set(allResults.map(r => r.backupFolder))].filter(Boolean).sort((a, b) => {
        const numA = parseInt(a.match(/\d+/)?.[0] || 0);
        const numB = parseInt(b.match(/\d+/)?.[0] || 0);
        return numA - numB;
    });

    console.log('Dashboard backup filter options (sorted):');
    backups.forEach((backup, index) => {
        console.log(`   ${index + 1}. ${backup}`);
    });

    const expectedDashboardOrder = ["Backup #1", "Backup #2", "Backup #3", "Backup #10", "Backup #15"];
    const isDashboardCorrect = JSON.stringify(backups) === JSON.stringify(expectedDashboardOrder);
    
    console.log(`Dashboard sorting: ${isDashboardCorrect ? '✅ CORRECT' : '❌ INCORRECT'}`);
    
    return isDashboardCorrect;
}

if (require.main === module) {
    const sortingCorrect = testBackupSorting();
    const dashboardCorrect = testDashboardSorting();
    
    console.log('\n🎯 Summary:');
    console.log(`Backup folder sorting: ${sortingCorrect ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Dashboard sorting: ${dashboardCorrect ? '✅ PASS' : '❌ FAIL'}`);
    
    if (sortingCorrect && dashboardCorrect) {
        console.log('\n🎉 All sorting tests passed!');
        console.log('\n💡 Benefits of numerical sorting:');
        console.log('   - Backups appear in chronological order (1, 2, 3, ..., 10, 11)');
        console.log('   - No more "Backup #10" appearing before "Backup #2"');
        console.log('   - Better user experience in dashboard filters');
        console.log('   - More logical backup progression display');
        
        console.log('\n💡 Benefits of real volume paths:');
        console.log('   - Direct search uses actual backup/volume IDs');
        console.log('   - Better search results with file-level hits');
        console.log('   - More accurate fallback search behavior');
        console.log('   - Improved search performance and accuracy');
        
        process.exit(0);
    } else {
        console.log('\n❌ Some tests failed. Check the implementation.');
        process.exit(1);
    }
}

module.exports = { testBackupSorting, testDashboardSorting };
