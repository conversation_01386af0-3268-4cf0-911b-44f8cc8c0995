{"name": "http-cookie-agent", "version": "5.0.4", "description": "Allows cookies with every Node.js HTTP clients.", "keywords": ["agent", "axios", "cookies", "fetch", "got", "http", "https", "needle", "node-fetch", "phin", "request", "superagent", "tough-cookie", "urllib", "undici"], "homepage": "https://github.com/3846masa/http-cookie-agent#readme", "bugs": {"url": "https://github.com/3846masa/http-cookie-agent/issues"}, "repository": {"type": "git", "url": "https://github.com/3846masa/http-cookie-agent.git"}, "funding": "https://github.com/sponsors/3846masa", "license": "MIT", "author": "3846masa <3846mas<PERSON><EMAIL>>", "exports": {"./http": "./http/index.js", "./undici": "./undici/index.js"}, "files": ["dist", "undici", "http", "!**/__tests__"], "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "npm-run-all build:*", "build:cjs": "babel src --out-dir dist --extensions .ts --out-file-extension .js", "build:mjs": "babel src --out-dir dist --extensions .mts --out-file-extension .mjs", "semantic-release": "semantic-release", "pretest": "npm run build", "test": "ava", "lint": "npm-run-all lint:*", "lint:tsc": "tsc --noEmit", "lint:eslint": "eslint --ext .js,.ts,.mjs,.mts .", "lint:prettier": "prettier --check .", "format": "npm-run-all format:*", "format:eslint": "eslint --fix --ext .js,.ts,.mjs,.mts .", "format:prettier": "prettier --write .", "patch-package": "patch-package"}, "ava": {"files": ["dist/**/__tests__/*.spec.js", "dist/**/__tests__/*.spec.mjs"], "workerThreads": false}, "resolutions": {"urllib/undici": "^5.22.1"}, "dependencies": {"agent-base": "^7.1.0"}, "devDependencies": {"@3846masa/configs": "github:3846masa/configs#707064cd4db30c36b728d63f5e411cc705c9ccad", "@babel/cli": "7.22.5", "@babel/core": "7.22.5", "@babel/preset-env": "7.22.5", "@babel/preset-typescript": "7.22.5", "@hapi/wreck": "18.0.1", "@semantic-release/changelog": "6.0.3", "@semantic-release/git": "10.0.1", "@types/deasync": "0.1.2", "@types/needle": "3.2.0", "@types/node": "14.18.51", "@types/request": "2.48.8", "@types/semver": "7.5.0", "@types/superagent": "4.1.18", "@types/tough-cookie": "4.0.2", "agentkeepalive": "4.3.0", "ava": "5.3.0", "axios": "1.4.0", "deasync": "0.1.28", "got": "12.6.1", "http-proxy-agent": "7.0.0", "needle": "3.2.0", "node-fetch": "3.3.1", "npm-run-all": "4.1.5", "patch-package": "7.0.0", "phin": "3.7.0", "proxy": "2.1.1", "request": "2.88.2", "rimraf": "5.0.1", "semantic-release": "19.0.5", "semver": "7.5.1", "superagent": "8.0.9", "tough-cookie": "4.1.3", "typescript": "5.1.3", "undici": "5.22.1", "urllib": "3.17.1"}, "peerDependencies": {"deasync": "^0.1.26", "tough-cookie": "^4.0.0", "undici": "^5.11.0"}, "peerDependenciesMeta": {"deasync": {"optional": true}, "undici": {"optional": true}}, "engines": {"node": ">=14.18.0 <15.0.0 || >=16.0.0"}}