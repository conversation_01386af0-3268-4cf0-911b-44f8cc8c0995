const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const axios = require('axios');
const { wrapper } = require('axios-cookiejar-support');
const { <PERSON>ieJar } = require('tough-cookie');
const fs = require('fs');
const path = require('path');

function generateRequestId() {
  return 'f' + Math.random().toString(16).substring(2, 17);
}

// Progressive result saving function
async function saveProgressiveResults(accountEmail, results, archiveId, archiveName) {
  try {
    const resultFile = 'search_results/RESULT.json';

    // Ensure directory exists
    const dir = path.dirname(resultFile);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    let existingData = { results: [], totalResults: 0, lastUpdated: new Date().toISOString() };

    // Read existing results if file exists
    if (fs.existsSync(resultFile)) {
      try {
        const fileContent = fs.readFileSync(resultFile, 'utf8');
        existingData = JSON.parse(fileContent);
      } catch (error) {
        console.log(`⚠️  [${accountEmail}] Could not read existing results file: ${error.message}`);
      }
    }

    // Add new results
    if (results && results.length > 0) {
      existingData.results.push(...results);
      existingData.totalResults = existingData.results.length;
      existingData.lastUpdated = new Date().toISOString();
      existingData.lastArchive = { archiveId, archiveName, accountEmail };

      // Write updated results back to file
      fs.writeFileSync(resultFile, JSON.stringify(existingData, null, 2));
      console.log(`💾 [${accountEmail}] Saved ${results.length} new results. Total: ${existingData.totalResults}`);
    }

    return existingData.totalResults;
  } catch (error) {
    console.error(`❌ [${accountEmail}] Failed to save progressive results: ${error.message}`);
    return 0;
  }
}

if (isMainThread) {
  // Main thread - export functions for use by multi_account_search.js
  
  async function searchArchiveWithSearcherAPIForVolume(client, archiveId, searchKeyword, accountEmail, backup, progressCallback = null) {
    console.log(`🔍 [${accountEmail}] Using Searcher API for backup ${backup.name} in archive ${archiveId} with keyword "${searchKeyword}"`);

    // Retry logic for searcher API - up to 3 retries
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount <= maxRetries) {
      try {
        if (retryCount > 0) {
          console.log(`🔄 [${accountEmail}] Searcher API retry attempt ${retryCount}/${maxRetries} for ${backup.name}`);
          await new Promise(resolve => setTimeout(resolve, 2000 * retryCount)); // Increasing delay
        }

        const requestId = generateRequestId();
        console.log(`🐛 DEBUG: [${accountEmail}] Making searcher API request for ${backup.name} with ID: ${requestId}`);

        const searchParams = {
          archiveId: archiveId,
          searchText: searchKeyword,
          path: backup.id || backup.name, // Use backup path if available
          limit: 500000,
          offset: 0
        };

        console.log(`🐛 DEBUG: [${accountEmail}] Searcher API params for ${backup.name}:`, JSON.stringify(searchParams, null, 2));

        const response = await client.get("https://cloud-wr-us2.acronis.com/ui/search", {
          headers: {
            "Accept": "application/json, text/plain, */*",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
            "Referer": "https://cloud-wr-us2.acronis.com/",
            "UI-REQUEST-ID": requestId,
          },
          params: searchParams
        });

        console.log(`🐛 DEBUG: [${accountEmail}] Searcher API response status for ${backup.name}: ${response.status}`);

        if (response.status === 200 && response.data && response.data.data) {
          const searchResults = response.data.data;
          console.log(`✅ [${accountEmail}] Searcher API found ${searchResults.length} results for ${backup.name}`);

          if (searchResults.length > 0) {
            const processedResults = searchResults.map(item => ({
              name: item.name,
              displayName: item.displayName || item.name,
              itemType: item.itemType,
              id: item.id,
              path: item.path || item.id,
              size: item.size || 0,
              type: item.type || 'unknown',
              lastModified: item.lastModified || null,
              backupFolder: backup.name,
              archiveName: archiveId.split('/').pop(),
              accountEmail: accountEmail,
              searchMetadata: {
                email: accountEmail,
                archiveId: archiveId,
                archiveName: archiveId.split('/').pop(),
                datacenter: archiveId.split('//')[1]?.split('/')[0] || 'unknown',
                searchTimestamp: new Date().toISOString(),
                searchKeyword: searchKeyword,
                searchMethod: 'searcher-api-volume',
                backupPath: backup.id || backup.name
              }
            }));

            // Save results immediately and call progress callback for live updates
            try {
              const totalSaved = await saveProgressiveResults(accountEmail, processedResults, archiveId, archiveId.split('/').pop());
              console.log(`💾 [${accountEmail}] Progressive save completed. Total results in file: ${totalSaved}`);
            } catch (saveError) {
              console.error(`❌ [${accountEmail}] Progressive save failed for ${backup.name}: ${saveError.message}`);
            }

            if (progressCallback) {
              try {
                console.log(`📊 [${accountEmail}] Calling progress callback with ${processedResults.length} results for ${backup.name}`);
                await progressCallback(accountEmail, [{
                  backupName: `${backup.name}-SearcherAPI`,
                  results: processedResults,
                  error: null,
                  message: `Searcher API found ${processedResults.length} results in ${backup.name}`,
                  archiveId: archiveId,
                  archiveName: archiveId.split('/').pop(),
                  datacenter: archiveId.split('//')[1]?.split('/')[0] || 'unknown'
                }]);
              } catch (callbackError) {
                console.error(`⚠️  [${accountEmail}] Progress callback failed for ${backup.name}: ${callbackError.message}`);
              }
            }

            return [{
              backupName: `${backup.name}-SearcherAPI`,
              results: processedResults,
              error: null,
              message: `Searcher API found ${processedResults.length} results in ${backup.name}`,
              archiveId: archiveId,
              archiveName: archiveId.split('/').pop(),
              datacenter: archiveId.split('//')[1]?.split('/')[0] || 'unknown'
            }];
          } else {
            console.log(`⚠️  [${accountEmail}] Searcher API returned no results for ${backup.name}`);
            return [];
          }
        } else {
          throw new Error(`Searcher API returned status ${response.status} for ${backup.name}`);
        }

      } catch (error) {
        retryCount++;

        const isRetryableError = error.message.includes('timeout') ||
                                error.message.includes('socket hang up') ||
                                error.message.includes('ECONNRESET') ||
                                error.message.includes('ENOTFOUND') ||
                                error.message.includes('ETIMEDOUT') ||
                                (error.response && error.response.status >= 500);

        if (isRetryableError && retryCount <= maxRetries) {
          console.log(`❌ [${accountEmail}] Searcher API retryable error for ${backup.name} (attempt ${retryCount}/${maxRetries + 1}): ${error.message}`);
          continue;
        } else if (retryCount > maxRetries) {
          console.log(`❌ [${accountEmail}] Searcher API max retries (${maxRetries}) exceeded for ${backup.name}: ${error.message}`);
        } else {
          console.log(`❌ [${accountEmail}] Searcher API non-retryable error for ${backup.name}: ${error.message}`);
        }

        // If searcher API fails completely, return empty results
        console.log(`⚠️  [${accountEmail}] Searcher API failed completely for ${backup.name}`);
        return [];
      }
    }

    return [];
  }

  async function searchArchiveWithSearcherAPI(client, archiveId, searchKeyword, accountEmail, progressCallback = null) {
    console.log(`🔍 [${accountEmail}] Using Searcher API for archive ${archiveId} with keyword "${searchKeyword}"`);
    
    // Retry logic for searcher API - up to 3 retries
    let retryCount = 0;
    const maxRetries = 3;
    
    while (retryCount <= maxRetries) {
      try {
        if (retryCount > 0) {
          console.log(`🔄 [${accountEmail}] Searcher API retry attempt ${retryCount}/${maxRetries}`);
          await new Promise(resolve => setTimeout(resolve, 2000 * retryCount)); // Increasing delay
        }

        const requestId = generateRequestId();
        console.log(`🐛 DEBUG: [${accountEmail}] Making searcher API request with ID: ${requestId}`);

        const searchParams = {
          archiveId: archiveId,
          searchText: searchKeyword,
          limit: 500000,
          offset: 0
        };

        console.log(`🐛 DEBUG: [${accountEmail}] Searcher API params:`, JSON.stringify(searchParams, null, 2));

        const response = await client.get("https://cloud-wr-us2.acronis.com/ui/search", {
          headers: {
            "Accept": "application/json, text/plain, */*",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
            "Referer": "https://cloud-wr-us2.acronis.com/",
            "UI-REQUEST-ID": requestId,
          },
          params: searchParams
        });

        console.log(`🐛 DEBUG: [${accountEmail}] Searcher API response status: ${response.status}`);

        if (response.status === 200 && response.data && response.data.data) {
          const searchResults = response.data.data;
          console.log(`✅ [${accountEmail}] Searcher API found ${searchResults.length} results`);

          if (searchResults.length > 0) {
            const processedResults = searchResults.map(item => ({
              name: item.name,
              displayName: item.displayName || item.name,
              itemType: item.itemType,
              id: item.id,
              path: item.path || item.id,
              size: item.size || 0,
              type: item.type || 'unknown',
              lastModified: item.lastModified || null,
              backupFolder: 'Searcher-API',
              archiveName: archiveId.split('/').pop(),
              accountEmail: accountEmail,
              searchMetadata: {
                email: accountEmail,
                archiveId: archiveId,
                archiveName: archiveId.split('/').pop(),
                datacenter: archiveId.split('//')[1]?.split('/')[0] || 'unknown',
                searchTimestamp: new Date().toISOString(),
                searchKeyword: searchKeyword,
                searchMethod: 'searcher-api'
              }
            }));

            // Save results immediately and call progress callback for live updates
            try {
              const totalSaved = await saveProgressiveResults(accountEmail, processedResults, archiveId, archiveId.split('/').pop());
              console.log(`💾 [${accountEmail}] Progressive save completed. Total results in file: ${totalSaved}`);
            } catch (saveError) {
              console.error(`❌ [${accountEmail}] Progressive save failed: ${saveError.message}`);
            }

            if (progressCallback) {
              try {
                console.log(`📊 [${accountEmail}] Calling progress callback with ${processedResults.length} results`);
                await progressCallback(accountEmail, [{
                  backupName: 'Searcher-API',
                  results: processedResults,
                  error: null,
                  message: `Searcher API found ${processedResults.length} results`,
                  archiveId: archiveId,
                  archiveName: archiveId.split('/').pop(),
                  datacenter: archiveId.split('//')[1]?.split('/')[0] || 'unknown'
                }]);
              } catch (callbackError) {
                console.error(`⚠️  [${accountEmail}] Progress callback failed: ${callbackError.message}`);
              }
            }

            return [{
              backupName: 'Searcher-API',
              results: processedResults,
              error: null,
              message: `Searcher API found ${processedResults.length} results`,
              archiveId: archiveId,
              archiveName: archiveId.split('/').pop(),
              datacenter: archiveId.split('//')[1]?.split('/')[0] || 'unknown'
            }];
          } else {
            console.log(`⚠️  [${accountEmail}] Searcher API returned no results`);
            return [];
          }
        } else {
          throw new Error(`Searcher API returned status ${response.status}`);
        }

      } catch (error) {
        retryCount++;
        
        const isRetryableError = error.message.includes('timeout') || 
                                error.message.includes('socket hang up') ||
                                error.message.includes('ECONNRESET') ||
                                error.message.includes('ENOTFOUND') ||
                                error.message.includes('ETIMEDOUT') ||
                                (error.response && error.response.status >= 500);

        if (isRetryableError && retryCount <= maxRetries) {
          console.log(`❌ [${accountEmail}] Searcher API retryable error (attempt ${retryCount}/${maxRetries + 1}): ${error.message}`);
          continue;
        } else if (retryCount > maxRetries) {
          console.log(`❌ [${accountEmail}] Searcher API max retries (${maxRetries}) exceeded: ${error.message}`);
        } else {
          console.log(`❌ [${accountEmail}] Searcher API non-retryable error: ${error.message}`);
        }
        
        // If searcher API fails completely, return empty results
        console.log(`⚠️  [${accountEmail}] Searcher API failed completely for archive ${archiveId}`);
        return [];
      }
    }

    return [];
  }

  async function searchInArchiveOptimized(client, cookies, archiveId, searchKeyword, accountEmail, progressCallback = null) {
    try {
      console.log(`🔍 [${accountEmail}] Starting volume-first optimized search in archive ${archiveId} for "${searchKeyword}"`);

      // Step 1: Volume-based search as primary method
      console.log(`🎯 [${accountEmail}] Step 1: Volume-based search (primary method)`);

      // Import the original search function
      const { searchInArchive: originalSearchInArchive } = require('./fast_search.js');
      const volumeResults = await originalSearchInArchive(client, cookies, archiveId, searchKeyword, accountEmail, progressCallback);

      // Step 2: After volume search, run searcher API for each discovered volume
      console.log(`🎯 [${accountEmail}] Step 2: Running searcher API for each volume location`);

      let allResults = volumeResults || [];
      let totalVolumeResults = allResults.reduce((sum, backup) => sum + backup.results.length, 0);
      console.log(`✅ [${accountEmail}] Volume-based search found: ${totalVolumeResults} results`);

      // Get backup folders to run searcher API per volume
      // Extract backup folders from volume results
      const backupFolders = [];
      if (volumeResults && volumeResults.length > 0) {
        volumeResults.forEach(result => {
          if (result.backupName && !backupFolders.find(b => b.name === result.backupName)) {
            backupFolders.push({ name: result.backupName, id: result.backupName });
          }
        });
      }

      if (backupFolders && backupFolders.length > 0) {
        console.log(`🔍 [${accountEmail}] Running searcher API for ${backupFolders.length} backup folders`);

        for (const backup of backupFolders) {
          try {
            console.log(`🎯 [${accountEmail}] Searcher API for backup: ${backup.name}`);

            // Try searcher API with backup-specific path
            const searcherResults = await searchArchiveWithSearcherAPIForVolume(
              client, archiveId, searchKeyword, accountEmail, backup, progressCallback
            );

            if (searcherResults && searcherResults.length > 0) {
              allResults.push(...searcherResults);
              const newResults = searcherResults.reduce((sum, result) => sum + result.results.length, 0);
              console.log(`✅ [${accountEmail}] Searcher API added ${newResults} results for ${backup.name}`);
            }

          } catch (error) {
            console.log(`⚠️  [${accountEmail}] Searcher API failed for backup ${backup.name}: ${error.message}`);
          }
        }
      }

      const finalTotal = allResults.reduce((sum, backup) => sum + backup.results.length, 0);
      console.log(`✅ [${accountEmail}] Combined search completed: ${finalTotal} total results`);
      return allResults;

    } catch (error) {
      console.error(`❌ [${accountEmail}] Optimized archive search failed:`, error.message);
      return [];
    }
  }

  module.exports = {
    searchInArchiveOptimized,
    searchArchiveWithSearcherAPI,
    searchArchiveWithSearcherAPIForVolume,
    saveProgressiveResults,
    generateRequestId
  };

} else {
  // Worker thread - this shouldn't be used in the optimized version
  // but keeping for compatibility
  console.log('Worker thread started for optimized search');
  parentPort.postMessage({ success: false, error: 'Optimized search does not use worker threads for searcher API' });
}
