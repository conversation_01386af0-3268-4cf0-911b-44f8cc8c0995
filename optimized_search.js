const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const axios = require('axios');
const { wrapper } = require('axios-cookiejar-support');
const { <PERSON>ieJar } = require('tough-cookie');

function generateRequestId() {
  return 'f' + Math.random().toString(16).substring(2, 17);
}

if (isMainThread) {
  // Main thread - export functions for use by multi_account_search.js
  
  async function searchArchiveWithSearcherAPI(client, archiveId, searchKeyword, accountEmail, progressCallback = null) {
    console.log(`🔍 [${accountEmail}] Using Searcher API for archive ${archiveId} with keyword "${searchKeyword}"`);
    
    // Retry logic for searcher API - up to 3 retries
    let retryCount = 0;
    const maxRetries = 3;
    
    while (retryCount <= maxRetries) {
      try {
        if (retryCount > 0) {
          console.log(`🔄 [${accountEmail}] Searcher API retry attempt ${retryCount}/${maxRetries}`);
          await new Promise(resolve => setTimeout(resolve, 2000 * retryCount)); // Increasing delay
        }

        const requestId = generateRequestId();
        console.log(`🐛 DEBUG: [${accountEmail}] Making searcher API request with ID: ${requestId}`);

        const searchParams = {
          archiveId: archiveId,
          searchText: searchKeyword,
          limit: 500000,
          offset: 0
        };

        console.log(`🐛 DEBUG: [${accountEmail}] Searcher API params:`, JSON.stringify(searchParams, null, 2));

        const response = await client.get("https://cloud-wr-us2.acronis.com/ui/search", {
          headers: {
            "Accept": "application/json, text/plain, */*",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
            "Referer": "https://cloud-wr-us2.acronis.com/",
            "UI-REQUEST-ID": requestId,
          },
          params: searchParams
        });

        console.log(`🐛 DEBUG: [${accountEmail}] Searcher API response status: ${response.status}`);

        if (response.status === 200 && response.data && response.data.data) {
          const searchResults = response.data.data;
          console.log(`✅ [${accountEmail}] Searcher API found ${searchResults.length} results`);

          if (searchResults.length > 0) {
            const processedResults = searchResults.map(item => ({
              name: item.name,
              displayName: item.displayName || item.name,
              itemType: item.itemType,
              id: item.id,
              path: item.path || item.id,
              size: item.size || 0,
              type: item.type || 'unknown',
              lastModified: item.lastModified || null,
              backupFolder: 'Searcher-API',
              archiveName: archiveId.split('/').pop(),
              accountEmail: accountEmail,
              searchMetadata: {
                email: accountEmail,
                archiveId: archiveId,
                archiveName: archiveId.split('/').pop(),
                datacenter: archiveId.split('//')[1]?.split('/')[0] || 'unknown',
                searchTimestamp: new Date().toISOString(),
                searchKeyword: searchKeyword,
                searchMethod: 'searcher-api'
              }
            }));

            // Call progress callback immediately for live updates
            if (progressCallback) {
              try {
                console.log(`📊 [${accountEmail}] Calling progress callback with ${processedResults.length} results`);
                await progressCallback(accountEmail, [{
                  backupName: 'Searcher-API',
                  results: processedResults,
                  error: null,
                  message: `Searcher API found ${processedResults.length} results`,
                  archiveId: archiveId,
                  archiveName: archiveId.split('/').pop(),
                  datacenter: archiveId.split('//')[1]?.split('/')[0] || 'unknown'
                }]);
              } catch (callbackError) {
                console.error(`⚠️  [${accountEmail}] Progress callback failed: ${callbackError.message}`);
              }
            }

            return [{
              backupName: 'Searcher-API',
              results: processedResults,
              error: null,
              message: `Searcher API found ${processedResults.length} results`,
              archiveId: archiveId,
              archiveName: archiveId.split('/').pop(),
              datacenter: archiveId.split('//')[1]?.split('/')[0] || 'unknown'
            }];
          } else {
            console.log(`⚠️  [${accountEmail}] Searcher API returned no results`);
            return [];
          }
        } else {
          throw new Error(`Searcher API returned status ${response.status}`);
        }

      } catch (error) {
        retryCount++;
        
        const isRetryableError = error.message.includes('timeout') || 
                                error.message.includes('socket hang up') ||
                                error.message.includes('ECONNRESET') ||
                                error.message.includes('ENOTFOUND') ||
                                error.message.includes('ETIMEDOUT') ||
                                (error.response && error.response.status >= 500);

        if (isRetryableError && retryCount <= maxRetries) {
          console.log(`❌ [${accountEmail}] Searcher API retryable error (attempt ${retryCount}/${maxRetries + 1}): ${error.message}`);
          continue;
        } else if (retryCount > maxRetries) {
          console.log(`❌ [${accountEmail}] Searcher API max retries (${maxRetries}) exceeded: ${error.message}`);
        } else {
          console.log(`❌ [${accountEmail}] Searcher API non-retryable error: ${error.message}`);
        }
        
        // If searcher API fails completely, return empty results
        console.log(`⚠️  [${accountEmail}] Searcher API failed completely for archive ${archiveId}`);
        return [];
      }
    }

    return [];
  }

  async function searchInArchiveOptimized(client, cookies, archiveId, searchKeyword, accountEmail, progressCallback = null) {
    try {
      console.log(`🔍 [${accountEmail}] Starting optimized search in archive ${archiveId} for "${searchKeyword}"`);

      // Step 1: Try Searcher API first (primary method)
      console.log(`🎯 [${accountEmail}] Step 1: Trying Searcher API (primary method)`);
      const searcherResults = await searchArchiveWithSearcherAPI(client, archiveId, searchKeyword, accountEmail, progressCallback);
      
      if (searcherResults && searcherResults.length > 0 && searcherResults[0].results.length > 0) {
        const totalResults = searcherResults.reduce((sum, backup) => sum + backup.results.length, 0);
        console.log(`✅ [${accountEmail}] Searcher API successful: ${totalResults} results found`);
        return searcherResults;
      }

      // Step 2: If Searcher API fails or returns no results, try volume-based search as fallback
      console.log(`🔄 [${accountEmail}] Step 2: Searcher API failed/empty, trying volume-based search as fallback`);
      
      // Import the original search function as fallback
      const { searchInArchive: originalSearchInArchive } = require('./fast_search.js');
      const fallbackResults = await originalSearchInArchive(client, cookies, archiveId, searchKeyword, accountEmail, progressCallback);
      
      if (fallbackResults && fallbackResults.length > 0) {
        const totalResults = fallbackResults.reduce((sum, backup) => sum + backup.results.length, 0);
        console.log(`✅ [${accountEmail}] Volume-based fallback successful: ${totalResults} results found`);
        return fallbackResults;
      }

      console.log(`⚠️  [${accountEmail}] Both Searcher API and volume-based search failed for archive ${archiveId}`);
      return [];

    } catch (error) {
      console.error(`❌ [${accountEmail}] Optimized archive search failed:`, error.message);
      return [];
    }
  }

  module.exports = {
    searchInArchiveOptimized,
    searchArchiveWithSearcherAPI,
    generateRequestId
  };

} else {
  // Worker thread - this shouldn't be used in the optimized version
  // but keeping for compatibility
  console.log('Worker thread started for optimized search');
  parentPort.postMessage({ success: false, error: 'Optimized search does not use worker threads for searcher API' });
}
