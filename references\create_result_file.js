const fs = require('fs');

// Read existing search data
const existingData = JSON.parse(fs.readFileSync('./search_results/search_program_2025-06-10T00-52-57-382Z.json', 'utf8'));

// Create RESULT.json structure
const resultData = {
  totalSearches: 1,
  totalResults: existingData.totalResults,
  lastUpdated: new Date().toISOString(),
  searches: [existingData]
};

// Write RESULT.json
fs.writeFileSync('./search_results/RESULT.json', JSON.stringify(resultData, null, 2));

console.log('✅ Created RESULT.json with existing search data');
console.log('📊 Total searches: 1');
console.log('📄 Total results:', existingData.totalResults);
console.log('📁 File: search_results/RESULT.json');
