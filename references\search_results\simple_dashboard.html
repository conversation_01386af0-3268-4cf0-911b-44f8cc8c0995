<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Acronis Search Results</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header h1 { color: #333; margin-bottom: 10px; }
        .stats { display: flex; gap: 20px; margin-top: 15px; }
        .stat { background: #f8f9fa; padding: 10px 15px; border-radius: 5px; }
        .stat-number { font-size: 1.5em; font-weight: bold; color: #007bff; }
        .stat-label { font-size: 0.9em; color: #666; }
        
        .search-box { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .search-input { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px; }
        .filters { display: flex; gap: 15px; margin-top: 15px; flex-wrap: wrap; }
        .filter-select { padding: 8px; border: 1px solid #ddd; border-radius: 5px; }
        
        .results-container { background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .results-header { background: #f8f9fa; padding: 15px; border-bottom: 1px solid #ddd; font-weight: bold; }
        .result-item { padding: 15px; border-bottom: 1px solid #eee; }
        .result-item:hover { background: #f8f9fa; }
        .result-item:last-child { border-bottom: none; }
        
        .result-name { font-weight: bold; color: #333; margin-bottom: 5px; font-size: 1.1em; }
        .result-path { color: #666; font-size: 0.9em; margin-bottom: 8px; word-break: break-all; }
        .result-details { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin-top: 10px; }
        .detail-item { background: #f8f9fa; padding: 8px; border-radius: 4px; font-size: 0.9em; }
        .detail-label { font-weight: bold; color: #555; }
        .detail-value { color: #333; }
        
        .pagination { text-align: center; padding: 20px; }
        .pagination button { padding: 8px 16px; margin: 0 5px; border: 1px solid #ddd; background: white; cursor: pointer; border-radius: 4px; }
        .pagination button.active { background: #007bff; color: white; border-color: #007bff; }
        .pagination button:hover { background: #f8f9fa; }
        
        .no-results { text-align: center; padding: 40px; color: #666; }
        .loading { text-align: center; padding: 40px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Acronis Search Results</h1>
            <p>Search keyword: <strong>"program"</strong> | Total: <span id="totalResults">Loading...</span> results</p>
            <div class="stats">
                <div class="stat">
                    <div class="stat-number" id="totalCount">-</div>
                    <div class="stat-label">Total Results</div>
                </div>
                <div class="stat">
                    <div class="stat-number" id="backupCount">-</div>
                    <div class="stat-label">Backups</div>
                </div>
                <div class="stat">
                    <div class="stat-number" id="searchTime">-</div>
                    <div class="stat-label">Search Time</div>
                </div>
                <div class="stat">
                    <div class="stat-number" id="filteredCount">-</div>
                    <div class="stat-label">Showing</div>
                </div>
            </div>
        </div>
        
        <div class="search-box">
            <input type="text" id="searchInput" class="search-input" placeholder="Search in results... (file name, path, backup folder)">
            <div class="filters">
                <select id="backupFilter" class="filter-select">
                    <option value="">All Backups</option>
                </select>
                <select id="typeFilter" class="filter-select">
                    <option value="">All Types</option>
                    <option value="Directory">Directories</option>
                    <option value="Regular">Files</option>
                </select>
                <select id="sizeFilter" class="filter-select">
                    <option value="">All Sizes</option>
                    <option value="0">Empty (0 bytes)</option>
                    <option value="small">Small (< 1MB)</option>
                    <option value="medium">Medium (1MB - 100MB)</option>
                    <option value="large">Large (> 100MB)</option>
                </select>
                <button onclick="clearFilters()" style="padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 5px; cursor: pointer;">Clear Filters</button>
            </div>
        </div>
        
        <div class="results-container">
            <div class="results-header">
                Search Results (<span id="resultCount">0</span> items)
            </div>
            <div id="resultsContainer">
                <div class="loading">Loading results...</div>
            </div>
            <div class="pagination" id="pagination"></div>
        </div>
    </div>

    <script>
        let allResults = [];
        let filteredResults = [];
        let currentPage = 1;
        const itemsPerPage = 50;

        // Load data - try multiple file paths
        async function loadData() {
            const possiblePaths = [
                './search_program_2025-06-10T00-52-57-382Z.json',
                'search_program_2025-06-10T00-52-57-382Z.json',
                '../search_program_2025-06-10T00-52-57-382Z.json'
            ];

            for (const path of possiblePaths) {
                try {
                    const response = await fetch(path);
                    if (response.ok) {
                        const data = await response.json();
                        processData(data);
                        return;
                    }
                } catch (error) {
                    console.log(`Failed to load from ${path}:`, error);
                }
            }

            // If all fetch attempts fail, show file upload option
            showFileUpload();
        }

        function processData(data) {
                // Flatten all results
                allResults = [];
                data.results.forEach(backup => {
                    backup.results.forEach(result => {
                        allResults.push(result);
                    });
                });
                
                // Update stats
                document.getElementById('totalCount').textContent = data.totalResults.toLocaleString();
                document.getElementById('backupCount').textContent = data.backupsSearched;
                document.getElementById('searchTime').textContent = data.searchDuration + 's';
                document.getElementById('totalResults').textContent = data.totalResults.toLocaleString();
                
                // Populate backup filter
                const backupFilter = document.getElementById('backupFilter');
                const backups = [...new Set(allResults.map(r => r.backupFolder))].sort();
                backups.forEach(backup => {
                    const option = document.createElement('option');
                    option.value = backup;
                    option.textContent = backup;
                    backupFilter.appendChild(option);
                });
                
                // Initial display
                filteredResults = [...allResults];
                updateDisplay();
        }

        function showFileUpload() {
            document.getElementById('resultsContainer').innerHTML = `
                <div style="text-align: center; padding: 40px;">
                    <h3>📁 Load Search Results</h3>
                    <p style="margin: 20px 0; color: #666;">
                        Could not automatically load the JSON file. Please select your search results file:
                    </p>
                    <input type="file" id="fileInput" accept=".json" style="margin: 20px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                    <br>
                    <button onclick="loadSelectedFile()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        Load File
                    </button>
                    <p style="margin-top: 20px; font-size: 0.9em; color: #888;">
                        Expected file: search_program_2025-06-10T00-52-57-382Z.json
                    </p>
                </div>
            `;
        }

        function loadSelectedFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];

            if (!file) {
                alert('Please select a JSON file');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);
                    processData(data);
                } catch (error) {
                    alert('Error parsing JSON file: ' + error.message);
                }
            };
            reader.readAsText(file);
        }

        // Start loading
        loadData();

        // Search and filter functions
        function filterResults() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const backupFilter = document.getElementById('backupFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;
            const sizeFilter = document.getElementById('sizeFilter').value;
            
            filteredResults = allResults.filter(result => {
                // Search term filter
                const matchesSearch = !searchTerm || 
                    result.name.toLowerCase().includes(searchTerm) ||
                    result.id.toLowerCase().includes(searchTerm) ||
                    result.backupFolder.toLowerCase().includes(searchTerm);
                
                // Backup filter
                const matchesBackup = !backupFilter || result.backupFolder === backupFilter;
                
                // Type filter
                const matchesType = !typeFilter || result.itemType === typeFilter;
                
                // Size filter
                let matchesSize = true;
                if (sizeFilter) {
                    const size = result.size || 0;
                    switch(sizeFilter) {
                        case '0': matchesSize = size === 0; break;
                        case 'small': matchesSize = size > 0 && size < 1024*1024; break;
                        case 'medium': matchesSize = size >= 1024*1024 && size < 100*1024*1024; break;
                        case 'large': matchesSize = size >= 100*1024*1024; break;
                    }
                }
                
                return matchesSearch && matchesBackup && matchesType && matchesSize;
            });
            
            currentPage = 1;
            updateDisplay();
        }

        function updateDisplay() {
            document.getElementById('filteredCount').textContent = filteredResults.length.toLocaleString();
            document.getElementById('resultCount').textContent = filteredResults.length.toLocaleString();
            
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageResults = filteredResults.slice(startIndex, endIndex);
            
            const container = document.getElementById('resultsContainer');
            
            if (pageResults.length === 0) {
                container.innerHTML = '<div class="no-results">No results found</div>';
            } else {
                container.innerHTML = pageResults.map(result => `
                    <div class="result-item">
                        <div class="result-name">📄 ${result.name}</div>
                        <div class="result-path">${result.id}</div>
                        <div class="result-details">
                            <div class="detail-item">
                                <span class="detail-label">Backup:</span>
                                <span class="detail-value">${result.backupFolder}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Type:</span>
                                <span class="detail-value">${result.itemType}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Size:</span>
                                <span class="detail-value">${formatFileSize(result.size || 0)}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Backup Date:</span>
                                <span class="detail-value">${formatDate(result.backupDate)}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Modified:</span>
                                <span class="detail-value">${formatDate(result.modifiedDate)}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Created:</span>
                                <span class="detail-value">${formatDate(result.creationDate)}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Group:</span>
                                <span class="detail-value">${result.itemGroup || 'N/A'}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Notarized:</span>
                                <span class="detail-value">${result.notarized ? 'Yes' : 'No'}</span>
                            </div>
                        </div>
                    </div>
                `).join('');
            }
            
            updatePagination();
        }

        function updatePagination() {
            const totalPages = Math.ceil(filteredResults.length / itemsPerPage);
            const pagination = document.getElementById('pagination');
            
            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }
            
            let paginationHTML = '';
            
            // Previous button
            if (currentPage > 1) {
                paginationHTML += `<button onclick="changePage(${currentPage - 1})">Previous</button>`;
            }
            
            // Page numbers
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);
            
            if (startPage > 1) {
                paginationHTML += `<button onclick="changePage(1)">1</button>`;
                if (startPage > 2) paginationHTML += `<span>...</span>`;
            }
            
            for (let i = startPage; i <= endPage; i++) {
                paginationHTML += `<button onclick="changePage(${i})" ${i === currentPage ? 'class="active"' : ''}>${i}</button>`;
            }
            
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) paginationHTML += `<span>...</span>`;
                paginationHTML += `<button onclick="changePage(${totalPages})">${totalPages}</button>`;
            }
            
            // Next button
            if (currentPage < totalPages) {
                paginationHTML += `<button onclick="changePage(${currentPage + 1})">Next</button>`;
            }
            
            pagination.innerHTML = paginationHTML;
        }

        function changePage(page) {
            currentPage = page;
            updateDisplay();
            window.scrollTo(0, 0);
        }

        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('backupFilter').value = '';
            document.getElementById('typeFilter').value = '';
            document.getElementById('sizeFilter').value = '';
            filterResults();
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatDate(dateString) {
            if (!dateString || dateString === '1970-01-01T00:00:00.000Z') return 'N/A';
            return new Date(dateString).toLocaleDateString() + ' ' + new Date(dateString).toLocaleTimeString();
        }

        // Event listeners
        document.getElementById('searchInput').addEventListener('input', filterResults);
        document.getElementById('backupFilter').addEventListener('change', filterResults);
        document.getElementById('typeFilter').addEventListener('change', filterResults);
        document.getElementById('sizeFilter').addEventListener('change', filterResults);
    </script>
</body>
</html>
