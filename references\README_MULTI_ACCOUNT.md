# Multi-Account Acronis Search System

A comprehensive search system that can process multiple Acronis accounts simultaneously, providing consolidated search results across all accounts and archives.

## 🚀 Features

### Multi-Account Processing
- **Batch Processing**: Process up to 20 accounts simultaneously
- **Parallel Workers**: 5 parallel workers per account (100 workers maximum)
- **Independent Authentication**: Each account authenticates separately
- **Graceful Error Handling**: Continue processing if some accounts fail

### Configuration Management
- **accounts.txt**: Store login credentials securely
- **config.json**: Centralized configuration for all settings
- **DEBUG_LOGS.txt**: Comprehensive logging for troubleshooting

### Search Capabilities
- **Multi-Keyword Search**: Search for multiple keywords simultaneously
- **Archive Discovery**: Automatically discover all archives per account
- **Volume Coverage**: Search all volumes in each archive
- **Progressive Updates**: Real-time updates to RESULT.json

### Enhanced Dashboard
- **Multi-Account View**: Consolidated results from all accounts
- **Account Filtering**: Filter results by specific accounts
- **Real-time Updates**: Auto-refresh for live monitoring
- **Detailed Metadata**: Login credentials and archive info for each result

## 📁 File Structure

```
├── accounts.txt              # Account credentials (email:password)
├── config.json              # System configuration
├── multi_account_search.js   # Main multi-account search engine
├── test_multi_account.js     # Test script for validation
├── fast_search.js           # Enhanced single-account search
├── parallel_search.js       # Parallel search workers
├── DEBUG_LOGS.txt           # Generated debug logs
└── search_results/
    ├── RESULT.json          # Consolidated search results
    ├── flexible_dashboard.html  # Enhanced dashboard
    └── serve_dashboard.js   # HTTP server for dashboard
```

## ⚙️ Configuration

### accounts.txt Format
```
# Acronis Account Credentials
# Format: email:password (one per line)
# Lines starting with # are comments

<EMAIL>:password123
<EMAIL>:mypassword
<EMAIL>:securepass
```

### config.json Settings
```json
{
  "searchSettings": {
    "keywords": ["microsoft", "adobe"],
    "searchTimeout": 900000
  },
  "concurrencySettings": {
    "maxConcurrentAccounts": 20,
    "workersPerAccount": 5
  },
  "outputSettings": {
    "resultFile": "search_results/RESULT.json",
    "debugLogFile": "DEBUG_LOGS.txt"
  }
}
```

## 🚀 Usage

### 1. Setup Accounts
Edit `accounts.txt` and add your Acronis account credentials:
```
<EMAIL>:your-password
<EMAIL>:another-password
```

### 2. Configure Search
Edit `config.json` to set your search keywords and preferences:
```json
{
  "searchSettings": {
    "keywords": ["your-search-term"]
  }
}
```

### 3. Test System
```bash
node test_multi_account.js
```

### 4. Run Multi-Account Search
```bash
node multi_account_search.js
```

### 5. View Results
```bash
# Start dashboard server
cd search_results
node serve_dashboard.js

# Open browser to http://localhost:8080
```

## 📊 Output Files

### RESULT.json Structure
```json
{
  "timestamp": "2025-06-10T05:59:01.026Z",
  "multiAccount": true,
  "totalAccounts": 3,
  "successfulAccounts": 2,
  "failedAccounts": 1,
  "totalResults": 15420,
  "keywords": ["microsoft"],
  "accounts": [
    {
      "email": "<EMAIL>",
      "success": true,
      "archives": 2,
      "results": 8500
    }
  ],
  "results": [...]
}
```

### DEBUG_LOGS.txt Format
```
[2025-06-10T05:59:01.026Z] [INFO] Starting multi-account search with 3 accounts
[2025-06-10T05:59:01.027Z] [SUCCESS] [<EMAIL>] Authentication successful
[2025-06-10T05:59:01.028Z] [ERROR] [<EMAIL>] Authentication failed: Invalid credentials
```

## 🔧 Advanced Configuration

### Concurrency Settings
- `maxConcurrentAccounts`: Maximum accounts processed simultaneously (default: 20)
- `workersPerAccount`: Parallel workers per account (default: 5)
- `delayBetweenAccounts`: Delay between account processing (default: 2000ms)

### Timeout Settings
- `searchTimeout`: Maximum time per search operation (default: 15 minutes)
- `loginTimeout`: Maximum time for authentication (default: 30 seconds)
- `volumeTimeout`: Maximum time per volume search (default: 15 minutes)

### Output Settings
- `enableDetailedLogging`: Include verbose debug information
- `preserveIndividualResults`: Keep separate result files per account
- `enableProgressiveUpdates`: Update RESULT.json during search

## 🛠️ Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Check credentials in accounts.txt
   - Verify account access to Acronis Cloud
   - Check DEBUG_LOGS.txt for specific error messages

2. **No Results Found**
   - Verify search keywords in config.json
   - Check if accounts have accessible archives
   - Review volume discovery logs

3. **Performance Issues**
   - Reduce maxConcurrentAccounts in config.json
   - Decrease workersPerAccount for slower systems
   - Increase delays between operations

### Debug Information
- All operations are logged to DEBUG_LOGS.txt
- Console output shows real-time progress
- Dashboard displays account-specific statistics

## 📈 Performance Optimization

### Recommended Settings
- **Small Scale** (1-5 accounts): maxConcurrentAccounts: 5, workersPerAccount: 3
- **Medium Scale** (5-15 accounts): maxConcurrentAccounts: 10, workersPerAccount: 5
- **Large Scale** (15+ accounts): maxConcurrentAccounts: 20, workersPerAccount: 5

### System Requirements
- **Memory**: 4GB+ RAM recommended for large-scale operations
- **Network**: Stable internet connection for API calls
- **Storage**: Sufficient space for RESULT.json (can be large with many results)

## 🔒 Security Considerations

- Store accounts.txt securely and restrict file permissions
- Consider using environment variables for sensitive credentials
- Regularly rotate account passwords
- Monitor DEBUG_LOGS.txt for authentication issues

## 🆕 New Features

- **Multi-account batch processing**
- **Enhanced dashboard with account filtering**
- **Comprehensive debug logging**
- **Progressive result updates**
- **Configurable concurrency settings**
- **Graceful error handling and recovery**
