# Clean Multi-Account Acronis Search

A production-ready, clean version of the multi-account Acronis search engine.

## Quick Start

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Configure accounts:**
   Edit `accounts.txt` and add your Acronis accounts:
   ```
   <EMAIL>:password1
   <EMAIL>:password2
   ```

3. **Configure search:**
   Edit `config.json` to set your search keywords:
   ```json
   {
     "searchSettings": {
       "keywords": ["microsoft", "program", "document"]
     }
   }
   ```

4. **Run search:**
   ```bash
   npm start
   ```

5. **View results:**
   ```bash
   npm run dashboard
   ```
   Then open http://localhost:8080

## Features

- ✅ Multi-account authentication
- ✅ Drive-based archive support
- ✅ Direct search API fallback
- ✅ Real-time dashboard
- ✅ Progressive result updates
- ✅ Clean, production-ready code

## Files

- `multi_account_search.js` - Main search engine
- `fast_search.js` - Archive search logic
- `parallel_search.js` - Parallel worker search
- `config.json` - Configuration settings
- `accounts.txt` - Account credentials
- `search_results/` - Dashboard and results

## Configuration

### Search Settings
- `keywords`: Array of search terms
- `searchTimeout`: Timeout per search (ms)

### Concurrency Settings
- `maxConcurrentAccounts`: Max parallel accounts
- `workersPerAccount`: Workers per account

### Authentication Settings
- `loginTimeout`: Login timeout (ms)
- `retryAttempts`: Number of retry attempts

## Output

Results are saved to `search_results/RESULT.json` and displayed in the web dashboard at http://localhost:8080

## Troubleshooting

### "No archives found" for accounts
This is normal and not an error. It means:
- The account has no backup archives/data
- The account hasn't used Acronis backup services
- The account exists but has no stored backups

### Authentication failures
- Verify credentials in `accounts.txt`
- Check if accounts have Acronis backup services enabled
- Some accounts may need 2FA or have different login requirements

### Dashboard not loading
- Ensure the dashboard server is running: `npm run dashboard`
- Check that port 8080 is available
- Try refreshing the browser or clearing cache

### No search results
- Verify keywords in `config.json` match your data
- Check that accounts have archives with searchable content
- Try broader search terms

## Support

This is a clean, minimal version with essential functionality only. All debugging comments and extra features have been removed for production use.
