<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Acronis Search Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        
        .file-loader { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .file-input { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 5px; margin-bottom: 10px; }
        .load-btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px; }
        .load-btn:hover { background: #0056b3; }
        .recent-files { margin-top: 15px; }
        .recent-file { display: inline-block; margin: 5px; padding: 5px 10px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 3px; cursor: pointer; font-size: 0.9em; }
        .recent-file:hover { background: #e9ecef; }
        
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); display: none; }
        .header h1 { color: #333; margin-bottom: 10px; }
        .stats { display: flex; gap: 20px; margin-top: 15px; flex-wrap: wrap; }
        .stat { background: #f8f9fa; padding: 10px 15px; border-radius: 5px; min-width: 120px; }
        .stat-number { font-size: 1.5em; font-weight: bold; color: #007bff; }
        .stat-label { font-size: 0.9em; color: #666; }
        
        .search-box { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); display: none; }
        .search-input { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px; margin-bottom: 15px; }
        .filters { display: flex; gap: 15px; flex-wrap: wrap; }
        .filter-select { padding: 8px; border: 1px solid #ddd; border-radius: 5px; min-width: 150px; }
        .clear-btn { padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 5px; cursor: pointer; }
        
        .results-container { background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); display: none; }
        .results-header { background: #f8f9fa; padding: 15px; border-bottom: 1px solid #ddd; font-weight: bold; }
        .result-item { padding: 15px; border-bottom: 1px solid #eee; }
        .result-item:hover { background: #f8f9fa; }
        .result-item:last-child { border-bottom: none; }
        
        .result-name { font-weight: bold; color: #333; margin-bottom: 5px; font-size: 1.1em; }
        .result-path { color: #666; font-size: 0.9em; margin-bottom: 10px; word-break: break-all; background: #f8f9fa; padding: 5px; border-radius: 3px; }
        .result-details { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 5px; font-size: 0.85em; }
        .detail-item { background: #f8f9fa; padding: 5px 8px; border-radius: 3px; }
        .detail-label { font-weight: bold; color: #555; }
        .detail-value { color: #333; margin-left: 5px; }
        
        .pagination { text-align: center; padding: 20px; }
        .pagination button { padding: 8px 16px; margin: 0 5px; border: 1px solid #ddd; background: white; cursor: pointer; border-radius: 4px; }
        .pagination button.active { background: #007bff; color: white; border-color: #007bff; }
        .pagination button:hover:not(.active) { background: #f8f9fa; }
        
        .no-results { text-align: center; padding: 40px; color: #666; }
        .error { color: #dc3545; margin-top: 10px; }
        .success { color: #28a745; margin-top: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="file-loader">
            <h2>📁 Acronis Search Dashboard</h2>
            <p style="margin-bottom: 15px; color: #666;">Automatically loading search results from RESULT.json:</p>

            <div style="margin-bottom: 15px;">
                <button onclick="loadResultsFile()" class="load-btn" style="background: #28a745;">🔄 Refresh Results</button>
                <button onclick="showAllSearches()" class="load-btn" style="background: #6f42c1;">📊 View All Searches</button>
                <button onclick="toggleAutoRefresh()" class="load-btn" style="background: #17a2b8;" id="autoRefreshBtn">⚡ Start Auto-Refresh (2s)</button>
            </div>

            <div id="fileStatus"></div>
            <div id="searchSummary" style="margin-top: 15px; display: none;">
                <p style="font-weight: bold; margin-bottom: 10px;">📈 Search History:</p>
                <div id="searchList"></div>
            </div>
        </div>
        
        <div class="header" id="header">
            <h1>🔍 Live Search Results</h1>
            <div id="liveStatus" style="background: #28a745; color: white; padding: 5px 10px; border-radius: 15px; display: inline-block; margin-bottom: 10px; font-size: 12px; font-weight: bold;">
                🔴 LIVE MODE ACTIVE
            </div>
            <p id="searchInfo">Loading...</p>
            <div class="stats" id="stats"></div>
        </div>
        
        <div class="search-box" id="searchBox">
            <input type="text" id="searchInput" class="search-input" placeholder="Search in results... (file name, path, backup folder)">
            <div class="filters">
                <select id="backupFilter" class="filter-select">
                    <option value="">All Backups</option>
                </select>
                <select id="accountFilter" class="filter-select">
                    <option value="">All Accounts</option>
                </select>
                <select id="typeFilter" class="filter-select">
                    <option value="">All Types</option>
                    <option value="Directory">Directories</option>
                    <option value="Regular">Files</option>
                </select>
                <select id="sizeFilter" class="filter-select">
                    <option value="">All Sizes</option>
                    <option value="0">Empty (0 bytes)</option>
                    <option value="small">Small (< 1MB)</option>
                    <option value="medium">Medium (1MB - 100MB)</option>
                    <option value="large">Large (> 100MB)</option>
                </select>
                <button onclick="clearFilters()" class="clear-btn">Clear Filters</button>
            </div>
        </div>
        
        <div class="results-container" id="resultsContainer">
            <div class="results-header">
                Search Results (<span id="resultCount">0</span> items)
            </div>
            <div id="resultsContent">
                <div class="no-results">No data loaded</div>
            </div>
            <div class="pagination" id="pagination"></div>
        </div>
    </div>

    <script>
        let allResults = [];
        let filteredResults = [];
        let currentPage = 1;
        let currentData = null;
        let allSearches = [];
        const itemsPerPage = 50;

        // Auto-refresh functionality
        let autoRefreshInterval = null;
        let lastUpdateTime = null;
        let searchInProgress = false;

        function toggleAutoRefresh() {
            const btn = document.getElementById('autoRefreshBtn');

            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                btn.textContent = '⚡ Start Auto-Refresh (2s)';
                btn.style.background = '#17a2b8';
                showStatus('Auto-refresh stopped', 'success');
            } else {
                autoRefreshInterval = setInterval(() => {
                    loadResultsFile();
                }, 2000); // Faster refresh for live updates
                btn.textContent = '⏹️ Stop Auto-Refresh';
                btn.style.background = '#dc3545';
                showStatus('🔴 LIVE: Auto-refresh started (every 2 seconds)', 'success');
            }
        }

        // Auto-start live refresh on page load
        function startLiveMode() {
            if (!autoRefreshInterval) {
                toggleAutoRefresh();
                showStatus('🔴 LIVE MODE: Dashboard will update automatically as search progresses', 'success');
            }
        }

        // Update live status indicator
        function updateLiveStatus(text, color) {
            const statusElement = document.getElementById('liveStatus');
            if (statusElement) {
                statusElement.textContent = text;
                statusElement.style.background = color;
            }
        }

        // Load RESULT.json file via HTTP
        function loadResultsFile() {
            const now = new Date().toISOString();

            fetch('./RESULT.json')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.text();
                })
                .then(text => {
                    // Try to parse JSON, handle partial writes gracefully
                    try {
                        return JSON.parse(text);
                    } catch (parseError) {
                        // If JSON is incomplete, wait and retry
                        if (text.trim() && !text.trim().endsWith('}')) {
                            throw new Error('JSON_INCOMPLETE');
                        }
                        throw parseError;
                    }
                })
                .then(data => {
                    console.log('DEBUG: Successfully loaded RESULT.json via HTTP');

                    // Check if this is a new update
                    const isNewUpdate = !lastUpdateTime || data.timestamp !== lastUpdateTime;
                    const isProgressive = data.progressiveUpdate === true;

                    if (isNewUpdate) {
                        lastUpdateTime = data.timestamp;

                        if (isProgressive) {
                            showStatus(`🔴 LIVE UPDATE: ${data.totalResults || 0} results found (${data.processedAccounts}/${data.totalAccounts} accounts processed)`, 'success');
                            searchInProgress = true;
                            updateLiveStatus(`🔴 SEARCHING: ${data.processedAccounts}/${data.totalAccounts} accounts`, '#ff6b35');
                        } else {
                            showStatus(`✅ SEARCH COMPLETE: ${data.totalResults || 0} total results from ${data.successfulAccounts} accounts`, 'success');
                            searchInProgress = false;
                            updateLiveStatus(`✅ COMPLETE: ${data.totalResults} results`, '#28a745');
                        }
                    } else if (searchInProgress) {
                        showStatus(`🔄 Searching... (${data.totalResults || 0} results so far)`, 'info');
                    }

                    processLoadedData(data, 'RESULT.json');
                })
                .catch(error => {
                    console.error('DEBUG: Failed to load RESULT.json:', error);
                    if (error.message.includes('404')) {
                        showStatus('⏳ Waiting for search to start...', 'info');
                    } else if (error.message === 'JSON_INCOMPLETE') {
                        showStatus('🔄 File being updated, retrying...', 'info');
                        // Retry after a short delay
                        setTimeout(() => loadResultsFile(), 500);
                    } else if (error.message.includes('Unexpected token')) {
                        showStatus('🔄 File being written, retrying...', 'info');
                        // Retry after a short delay for JSON parse errors
                        setTimeout(() => loadResultsFile(), 500);
                    } else {
                        showStatus('Failed to load RESULT.json: ' + error.message, 'error');
                    }
                });
        }

        // Process loaded data (common function)
        function processLoadedData(data, filename) {
            if (data.searches && data.searches.length > 0) {
                allSearches = data.searches;
                // Use the latest search by default
                const latestSearch = data.searches[data.searches.length - 1];
                processData(latestSearch, filename + ' (Latest Search)');
                updateSearchSummary(data);
                showStatus(`Loaded ${data.totalSearches} searches with ${data.totalResults} total results from ${filename}`, 'success');
            } else if (data.results && Array.isArray(data.results)) {
                // Handle single search format
                allSearches = [data];
                processData(data, filename);
                showStatus(`Loaded single search with ${data.totalResults || 'unknown'} results from ${filename}`, 'success');
            } else {
                showStatus('No valid search data found in ' + filename, 'error');
            }
        }

        // Show all searches summary
        function showAllSearches() {
            const summaryDiv = document.getElementById('searchSummary');
            if (summaryDiv.style.display === 'none') {
                summaryDiv.style.display = 'block';
            } else {
                summaryDiv.style.display = 'none';
            }
        }

        // Update search summary
        function updateSearchSummary(data) {
            const searchList = document.getElementById('searchList');
            searchList.innerHTML = data.searches.map((search, index) => `
                <div class="recent-file" onclick="loadSpecificSearch(${index})" style="display: block; margin: 5px 0; padding: 10px; background: #f8f9fa; border-radius: 5px; cursor: pointer;">
                    <strong>${search.keyword || 'Unknown'}</strong> - ${search.totalResults} results - ${new Date(search.timestamp).toLocaleDateString()}
                    <br><small style="color: #666;">Duration: ${search.searchDuration}s | Backups: ${search.backupsSearched}</small>
                </div>
            `).join('');
        }

        // Load specific search
        function loadSpecificSearch(index) {
            if (allSearches[index]) {
                processData(allSearches[index], `RESULT.json (Search #${index + 1})`);
                showStatus(`Loaded search #${index + 1}: "${allSearches[index].keyword}"`, 'success');
            }
        }

        // Auto-load on page start
        window.addEventListener('load', function() {
            showStatus('🚀 Live Search Dashboard loaded. Starting live mode...', 'success');

            // Load immediately
            loadResultsFile();

            // Auto-start live mode for seamless experience
            setTimeout(() => {
                startLiveMode();
                showStatus('🔴 LIVE MODE ACTIVE: Dashboard will update automatically as search progresses', 'success');
            }, 1000);
        });

        // Process loaded data
        function processData(data, filename) {
            console.log('DEBUG: processData called with:', data, filename);
            currentData = data;

            // Flatten all results
            allResults = [];

            if (data.multiAccount && data.results && Array.isArray(data.results)) {
                console.log('DEBUG: Processing multi-account data with', data.results.length, 'backup entries');

                // For multi-account format, each entry in results is a backup with its own results array
                data.results.forEach((backup, index) => {
                    console.log(`DEBUG: Processing backup ${index + 1}:`, backup.backupName, 'from archive:', backup.archiveName);
                    console.log(`DEBUG: Backup has ${backup.results?.length || 0} file results`);

                    if (backup.results && Array.isArray(backup.results)) {
                        backup.results.forEach(fileResult => {
                            // Add backup context to each file result
                            fileResult.backupFolder = backup.backupName;
                            fileResult.archiveName = backup.archiveName;
                            fileResult.accountEmail = backup.accountEmail;
                            allResults.push(fileResult);
                        });
                    } else if (backup.error) {
                        // Show backup entries even if they have errors
                        console.log(`DEBUG: Backup ${backup.backupName} has error:`, backup.error);
                        // Create a placeholder entry for failed backups
                        allResults.push({
                            name: `${backup.backupName} (Error)`,
                            displayName: `${backup.backupName} - ${backup.error}`,
                            itemType: 'Error',
                            backupFolder: backup.backupName,
                            archiveName: backup.archiveName,
                            accountEmail: backup.accountEmail,
                            error: backup.error,
                            id: `${backup.backupName}/Error`
                        });
                    }
                });
            } else if (data.results && Array.isArray(data.results)) {
                console.log('DEBUG: Processing single-account data with', data.results.length, 'backups');
                data.results.forEach(backup => {
                    console.log('DEBUG: Processing backup:', backup.backupName, 'with', backup.results?.length, 'results');
                    if (backup.results && Array.isArray(backup.results)) {
                        backup.results.forEach(result => {
                            allResults.push(result);
                        });
                    }
                });
            } else {
                console.log('DEBUG: No results array found in data');
            }

            console.log('DEBUG: Total flattened results:', allResults.length);

            // Update UI
            updateHeader(data, filename);
            updateStats(data);
            populateFilters();

            // Show sections
            const headerEl = document.getElementById('header');
            const searchBoxEl = document.getElementById('searchBox');
            const resultsContainerEl = document.getElementById('resultsContainer');

            if (headerEl) headerEl.style.display = 'block';
            if (searchBoxEl) searchBoxEl.style.display = 'block';
            if (resultsContainerEl) resultsContainerEl.style.display = 'block';

            // Initial display
            filteredResults = [...allResults];
            updateDisplay();

            showStatus(`Loaded ${allResults.length} results from ${filename}`, 'success');
        }

        // Update header information
        function updateHeader(data, filename) {
            const keyword = data.keyword || data.keywords?.join(', ') || 'Unknown';
            const timestamp = data.timestamp ? new Date(data.timestamp).toLocaleString() : 'Unknown';

            let headerInfo = `File: <strong>${filename}</strong> | Keyword: <strong>"${keyword}"</strong> | Generated: ${timestamp}`;

            // Multi-account information
            if (data.multiAccount) {
                headerInfo += `<br>🏢 <strong>Multi-Account Search</strong>`;
                headerInfo += `<br>📊 Accounts: <strong>${data.totalAccounts}</strong> total, <strong>${data.successfulAccounts}</strong> successful, <strong>${data.failedAccounts}</strong> failed`;

                if (data.accounts && data.accounts.length > 0) {
                    const successfulEmails = data.accounts.filter(acc => acc.success).map(acc => acc.email);
                    if (successfulEmails.length > 0) {
                        headerInfo += `<br>✅ Successful: <strong>${successfulEmails.slice(0, 3).join(', ')}${successfulEmails.length > 3 ? ` +${successfulEmails.length - 3} more` : ''}</strong>`;
                    }
                }
            } else {
                // Single account information
                if (data.loginCredentials) {
                    headerInfo += `<br>Account: <strong>${data.loginCredentials.email}</strong>`;
                }

                if (data.archiveInfo && data.archiveInfo.length > 0) {
                    const archiveNames = data.archiveInfo.map(arch => arch.machineName).join(', ');
                    headerInfo += `<br>Archives: <strong>${archiveNames}</strong>`;
                }
            }

            document.getElementById('searchInfo').innerHTML = headerInfo;
        }

        // Update statistics
        function updateStats(data) {
            const statsContainer = document.getElementById('stats');
            let stats = [];

            if (data.multiAccount) {
                // Multi-account statistics
                stats = [
                    { label: 'Total Results', value: (data.totalResults || allResults.length).toLocaleString() },
                    { label: 'Accounts', value: `${data.successfulAccounts}/${data.totalAccounts}` },
                    { label: 'Search Time', value: data.searchDuration ? data.searchDuration + 's' : 'N/A' },
                    { label: 'Showing', value: allResults.length.toLocaleString(), id: 'filteredCount' }
                ];
            } else {
                // Single account statistics
                stats = [
                    { label: 'Total Results', value: (data.totalResults || allResults.length).toLocaleString() },
                    { label: 'Backups', value: data.backupsSearched || data.results?.length || 'N/A' },
                    { label: 'Search Time', value: data.searchDuration ? data.searchDuration + 's' : 'N/A' },
                    { label: 'Showing', value: allResults.length.toLocaleString(), id: 'filteredCount' }
                ];
            }

            statsContainer.innerHTML = stats.map(stat => `
                <div class="stat">
                    <div class="stat-number" ${stat.id ? `id="${stat.id}"` : ''}>${stat.value}</div>
                    <div class="stat-label">${stat.label}</div>
                </div>
            `).join('');
        }

        // Populate filter dropdowns
        function populateFilters() {
            // Clear existing options
            const backupFilter = document.getElementById('backupFilter');
            backupFilter.innerHTML = '<option value="">All Backups</option>';
            
            // Get unique backup folders
            const backups = [...new Set(allResults.map(r => r.backupFolder))].filter(Boolean).sort((a, b) => {
                const numA = parseInt(a.match(/\d+/)?.[0] || 0);
                const numB = parseInt(b.match(/\d+/)?.[0] || 0);
                return numA - numB;
            });
            
            backups.forEach(backup => {
                const option = document.createElement('option');
                option.value = backup;
                option.textContent = backup;
                backupFilter.appendChild(option);
            });
        }

        // Filter results
        function filterResults() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const backupFilter = document.getElementById('backupFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;
            const sizeFilter = document.getElementById('sizeFilter').value;
            
            filteredResults = allResults.filter(result => {
                // Search term filter
                const matchesSearch = !searchTerm || 
                    (result.name && result.name.toLowerCase().includes(searchTerm)) ||
                    (result.id && result.id.toLowerCase().includes(searchTerm)) ||
                    (result.backupFolder && result.backupFolder.toLowerCase().includes(searchTerm));
                
                // Backup filter
                const matchesBackup = !backupFilter || result.backupFolder === backupFilter;
                
                // Type filter
                const matchesType = !typeFilter || result.itemType === typeFilter;
                
                // Size filter
                let matchesSize = true;
                if (sizeFilter) {
                    const size = result.size || 0;
                    switch(sizeFilter) {
                        case '0': matchesSize = size === 0; break;
                        case 'small': matchesSize = size > 0 && size < 1024*1024; break;
                        case 'medium': matchesSize = size >= 1024*1024 && size < 100*1024*1024; break;
                        case 'large': matchesSize = size >= 100*1024*1024; break;
                    }
                }
                
                return matchesSearch && matchesBackup && matchesType && matchesSize;
            });
            
            currentPage = 1;
            updateDisplay();
        }

        // Update display
        function updateDisplay() {
            console.log('DEBUG: updateDisplay called with', filteredResults.length, 'filtered results');

            const filteredCountEl = document.getElementById('filteredCount');
            if (filteredCountEl) {
                filteredCountEl.textContent = filteredResults.length.toLocaleString();
            }

            const resultCountEl = document.getElementById('resultCount');
            if (resultCountEl) {
                resultCountEl.textContent = filteredResults.length.toLocaleString();
            }

            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageResults = filteredResults.slice(startIndex, endIndex);

            console.log('DEBUG: Showing page results:', pageResults.length, 'items');

            const container = document.getElementById('resultsContent');
            if (!container) {
                console.error('DEBUG: resultsContent element not found!');
                return;
            }

            if (pageResults.length === 0) {
                container.innerHTML = '<div class="no-results">No results found</div>';
            } else {
                container.innerHTML = pageResults.map(result => `
                    <div class="result-item">
                        <div class="result-name">${result.itemType === 'Directory' ? '📁' : '📄'} ${result.name || 'Unnamed'}</div>
                        <div class="result-path">${result.id || 'No path'}</div>
                        <div class="result-details">
                            <div class="detail-item">
                                <span class="detail-label">Backup:</span><span class="detail-value">${result.backupFolder || 'N/A'}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Type:</span><span class="detail-value">${result.itemType || 'N/A'}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Size:</span><span class="detail-value">${formatFileSize(result.size || 0)}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Backup Date:</span><span class="detail-value">${formatDate(result.backupDate)}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Modified:</span><span class="detail-value">${formatDate(result.modifiedDate)}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Created:</span><span class="detail-value">${formatDate(result.creationDate)}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Group:</span><span class="detail-value">${result.itemGroup || 'N/A'}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Notarized:</span><span class="detail-value">${result.notarized ? 'Yes' : 'No'}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Archive:</span><span class="detail-value">${result.searchMetadata?.archiveName || result.archiveName || 'N/A'}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Account:</span><span class="detail-value">${result.searchMetadata?.email || result.accountEmail || 'N/A'}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Volume:</span><span class="detail-value">${result.volumeName || 'N/A'}</span>
                            </div>
                            ${result.error ? `
                            <div class="detail-item">
                                <span class="detail-label">Error:</span><span class="detail-value" style="color: #dc3545;">${result.error}</span>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                `).join('');
            }
            
            updatePagination();
        }

        // Pagination logic
        function updatePagination() {
            const totalPages = Math.ceil(filteredResults.length / itemsPerPage);
            const pagination = document.getElementById('pagination');
            
            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }
            
            let paginationHTML = '';
            
            if (currentPage > 1) {
                paginationHTML += `<button onclick="changePage(${currentPage - 1})">Previous</button>`;
            }
            
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);
            
            if (startPage > 1) {
                paginationHTML += `<button onclick="changePage(1)">1</button>`;
                if (startPage > 2) paginationHTML += `<span>...</span>`;
            }
            
            for (let i = startPage; i <= endPage; i++) {
                paginationHTML += `<button onclick="changePage(${i})" ${i === currentPage ? 'class="active"' : ''}>${i}</button>`;
            }
            
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) paginationHTML += `<span>...</span>`;
                paginationHTML += `<button onclick="changePage(${totalPages})">${totalPages}</button>`;
            }
            
            if (currentPage < totalPages) {
                paginationHTML += `<button onclick="changePage(${currentPage + 1})">Next</button>`;
            }
            
            pagination.innerHTML = paginationHTML;
        }

        // Utility functions
        function changePage(page) {
            currentPage = page;
            updateDisplay();
            window.scrollTo(0, 0);
        }

        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('backupFilter').value = '';
            document.getElementById('typeFilter').value = '';
            document.getElementById('sizeFilter').value = '';
            filterResults();
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatDate(dateString) {
            if (!dateString || dateString === '1970-01-01T00:00:00.000Z') return 'N/A';
            const date = new Date(dateString);
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        }

        function showStatus(message, type) {
            const statusEl = document.getElementById('fileStatus');
            statusEl.innerHTML = `<div class="${type}">${message}</div>`;
            setTimeout(() => statusEl.innerHTML = '', 5000);
        }

        function saveToRecent(filename, data) {
            // Could implement localStorage to remember recent files
            console.log('File loaded:', filename);
        }

        // Event listeners
        document.getElementById('searchInput').addEventListener('input', filterResults);
        document.getElementById('backupFilter').addEventListener('change', filterResults);
        document.getElementById('typeFilter').addEventListener('change', filterResults);
        document.getElementById('sizeFilter').addEventListener('change', filterResults);
    </script>
</body>
</html>
