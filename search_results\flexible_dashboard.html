<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Acronis Search Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }

        .file-loader { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .file-input { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 5px; margin-bottom: 10px; }
        .load-btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px; }
        .load-btn:hover { background: #0056b3; }
        .recent-files { margin-top: 15px; }
        .recent-file { display: inline-block; margin: 5px; padding: 5px 10px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 3px; cursor: pointer; font-size: 0.9em; }
        .recent-file:hover { background: #e9ecef; }

        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); display: none; }
        .header h1 { color: #333; margin-bottom: 10px; }
        .stats { display: flex; gap: 20px; margin-top: 15px; flex-wrap: wrap; }
        .stat { background: #f8f9fa; padding: 10px 15px; border-radius: 5px; min-width: 120px; }
        .stat-number { font-size: 1.5em; font-weight: bold; color: #007bff; }
        .stat-label { font-size: 0.9em; color: #666; }

        .search-box { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); display: none; }
        .search-input { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px; margin-bottom: 15px; }
        .filters { display: flex; gap: 15px; flex-wrap: wrap; }
        .filter-select { padding: 8px; border: 1px solid #ddd; border-radius: 5px; min-width: 150px; }
        .clear-btn { padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 5px; cursor: pointer; }

        .results-container { background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); display: none; }
        .results-header { background: #f8f9fa; padding: 15px; border-bottom: 1px solid #ddd; font-weight: bold; }
        .result-item { padding: 15px; border-bottom: 1px solid #eee; }
        .result-item:hover { background: #f8f9fa; }
        .result-item:last-child { border-bottom: none; }

        .result-name { font-weight: bold; color: #333; margin-bottom: 5px; font-size: 1.1em; }
        .result-path { color: #666; font-size: 0.9em; margin-bottom: 10px; word-break: break-all; background: #f8f9fa; padding: 5px; border-radius: 3px; }
        .result-details { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 5px; font-size: 0.85em; }
        .detail-item { background: #f8f9fa; padding: 5px 8px; border-radius: 3px; }
        .detail-label { font-weight: bold; color: #555; }
        .detail-value { color: #333; margin-left: 5px; }

        .pagination { text-align: center; padding: 20px; }
        .pagination button { padding: 8px 16px; margin: 0 5px; border: 1px solid #ddd; background: white; cursor: pointer; border-radius: 4px; }
        .pagination button.active { background: #007bff; color: white; border-color: #007bff; }
        .pagination button:hover:not(.active) { background: #f8f9fa; }

        .no-results { text-align: center; padding: 40px; color: #666; }
        .error { color: #dc3545; margin-top: 10px; }
        .success { color: #28a745; margin-top: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="file-loader">
            <h2>📁 Acronis Search Dashboard</h2>
            <p style="margin-bottom: 15px; color: #666;">Automatically loading search results from RESULT.json:</p>

            <div style="margin-bottom: 15px;">
                <button onclick="loadResultsFile()" class="load-btn" style="background: #28a745;">🔄 Refresh Results</button>
                <button onclick="showAllSearches()" class="load-btn" style="background: #6f42c1;">📊 View All Searches</button>
                <button onclick="toggleAutoRefresh()" class="load-btn" style="background: #17a2b8;" id="autoRefreshBtn">⚡ Start Auto-Refresh (2s)</button>
            </div>

            <div id="fileStatus"></div>
            <div id="searchSummary" style="margin-top: 15px; display: none;">
                <p style="font-weight: bold; margin-bottom: 10px;">📈 Search History:</p>
                <div id="searchList"></div>
            </div>
        </div>

        <div class="header" id="header">
            <h1>🔍 Live Search Results</h1>
            <div id="liveStatus" style="background: #28a745; color: white; padding: 5px 10px; border-radius: 15px; display: inline-block; margin-bottom: 10px; font-size: 12px; font-weight: bold;">
                🔴 LIVE MODE ACTIVE
            </div>
            <p id="searchInfo">Loading...</p>
            <div class="stats" id="stats"></div>
        </div>

        <div class="search-box" id="searchBox">
            <input type="text" id="searchInput" class="search-input" placeholder="Search in results... (file name, path, backup folder)">
            <div class="filters">
                <select id="backupFilter" class="filter-select">
                    <option value="">All Backups</option>
                </select>
                <select id="accountFilter" class="filter-select">
                    <option value="">All Accounts</option>
                </select>
                <select id="typeFilter" class="filter-select">
                    <option value="">All Types</option>
                    <option value="Directory">Directories</option>
                    <option value="Regular">Files</option>
                </select>
                <select id="sizeFilter" class="filter-select">
                    <option value="">All Sizes</option>
                    <option value="0">Empty (0 bytes)</option>
                    <option value="small">Small (< 1MB)</option>
                    <option value="medium">Medium (1MB - 100MB)</option>
                    <option value="large">Large (> 100MB)</option>
                </select>
                <button onclick="clearFilters()" class="clear-btn">Clear Filters</button>
            </div>
        </div>

        <div class="results-container" id="resultsContainer">
            <div class="results-header">
                Search Results (<span id="resultCount">0</span> items)
            </div>
            <div id="resultsContent">
                <div class="no-results">No data loaded</div>
            </div>
            <div class="pagination" id="pagination"></div>
        </div>
    </div>

    <script>
        let allResults = [];
        let filteredResults = [];
        let currentPage = 1;
        let currentData = null;
        let allSearches = [];
        const itemsPerPage = 50;

        // Auto-refresh functionality
        let autoRefreshInterval = null;
        let lastUpdateTime = null;
        let searchInProgress = false;

        function toggleAutoRefresh() {
            const btn = document.getElementById('autoRefreshBtn');

            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                btn.textContent = '⚡ Start Auto-Refresh (2s)';
                btn.style.background = '#17a2b8';
                showStatus('Auto-refresh stopped', 'success');
            } else {
                autoRefreshInterval = setInterval(() => {
                    loadResultsFile();
                }, 2000); // Faster refresh for live updates
                btn.textContent = '⏹️ Stop Auto-Refresh';
                btn.style.background = '#dc3545';
                showStatus('🔴 LIVE: Auto-refresh started (every 2 seconds)', 'success');
            }
        }

        // Load RESULT.json file via HTTP
        function loadResultsFile() {
            const now = new Date().toISOString();

            fetch('./RESULT.json')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.text();
                })
                .then(text => {
                    // Try to parse JSON, handle partial writes gracefully
                    try {
                        return JSON.parse(text);
                    } catch (parseError) {
                        // If JSON is incomplete, wait and retry
                        if (text.trim() && !text.trim().endsWith('}')) {
                            throw new Error('JSON_INCOMPLETE');
                        }
                        throw parseError;
                    }
                })
                .then(data => {
                    console.log('DEBUG: Successfully loaded RESULT.json via HTTP');

                    // Check if this is a new update
                    const isNewUpdate = !lastUpdateTime || data.timestamp !== lastUpdateTime;
                    const isProgressive = data.progressiveUpdate === true;

                    if (isNewUpdate) {
                        lastUpdateTime = data.timestamp;

                        if (isProgressive) {
                            showStatus(`🔴 LIVE UPDATE: ${data.totalResults || 0} results found (${data.processedAccounts}/${data.totalAccounts} accounts processed)`, 'success');
                            searchInProgress = true;
                            updateLiveStatus(`🔴 SEARCHING: ${data.processedAccounts}/${data.totalAccounts} accounts`, '#ff6b35');
                        } else {
                            showStatus(`✅ SEARCH COMPLETE: ${data.totalResults || 0} total results from ${data.successfulAccounts} accounts`, 'success');
                            searchInProgress = false;
                            updateLiveStatus(`✅ COMPLETE: ${data.totalResults} results`, '#28a745');
                        }
                    } else if (searchInProgress) {
                        showStatus(`🔄 Searching... (${data.totalResults || 0} results so far)`, 'info');
                    }

                    processLoadedData(data, 'RESULT.json');
                })
                .catch(error => {
                    console.error('DEBUG: Failed to load RESULT.json:', error);
                    if (error.message.includes('404')) {
                        showStatus('⏳ Waiting for search to start...', 'info');
                    } else if (error.message === 'JSON_INCOMPLETE') {
                        showStatus('🔄 File being updated, retrying...', 'info');
                        // Retry after a short delay
                        setTimeout(() => loadResultsFile(), 500);
                    } else if (error.message.includes('Unexpected token')) {
                        showStatus('🔄 File being written, retrying...', 'info');
                        // Retry after a short delay for JSON parse errors
                        setTimeout(() => loadResultsFile(), 500);
                    } else {
                        showStatus('Failed to load RESULT.json: ' + error.message, 'error');
                    }
                });
        }

        // Show status messages
        function showStatus(message, type) {
            const statusDiv = document.getElementById('fileStatus');
            statusDiv.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Update live status indicator
        function updateLiveStatus(text, color) {
            const statusElement = document.getElementById('liveStatus');
            if (statusElement) {
                statusElement.textContent = text;
                statusElement.style.background = color;
            }
        }

        // Process loaded data and display it
        function processLoadedData(data, filename) {
            currentData = data;

            // Store this search in history
            const searchId = data.timestamp || new Date().toISOString();
            const existingIndex = allSearches.findIndex(s => s.id === searchId);

            if (existingIndex === -1) {
                allSearches.unshift({
                    id: searchId,
                    filename: filename,
                    data: data,
                    timestamp: new Date(data.timestamp || Date.now()).toLocaleString()
                });

                // Keep only last 10 searches
                if (allSearches.length > 10) {
                    allSearches = allSearches.slice(0, 10);
                }
            } else {
                // Update existing search
                allSearches[existingIndex].data = data;
            }

            // Extract all individual results
            allResults = [];
            if (data.results && Array.isArray(data.results)) {
                data.results.forEach(backup => {
                    if (backup.results && Array.isArray(backup.results)) {
                        backup.results.forEach(file => {
                            allResults.push({
                                ...file,
                                backupFolder: backup.backupName || 'Unknown Backup',
                                accountEmail: backup.accountEmail || file.accountEmail || 'Unknown Account',
                                archiveName: backup.archiveName || file.archiveName || 'Unknown Archive',
                                searchKeyword: backup.searchKeyword || 'Unknown'
                            });
                        });
                    }
                });
            }

            // Update UI
            updateHeader(data);
            updateFilters();
            applyFilters();

            // Show the interface
            document.getElementById('header').style.display = 'block';
            document.getElementById('searchBox').style.display = 'block';
            document.getElementById('resultsContainer').style.display = 'block';
        }

        // Update header with search info
        function updateHeader(data) {
            const keywords = data.keywords ? data.keywords.join(', ') : 'N/A';
            const timestamp = data.timestamp ? new Date(data.timestamp).toLocaleString() : 'Unknown';
            const duration = data.searchDuration ? `${Math.floor(data.searchDuration / 60)}m ${data.searchDuration % 60}s` : 'Unknown';

            document.getElementById('searchInfo').innerHTML = `
                <strong>Keywords:</strong> ${keywords} |
                <strong>Time:</strong> ${timestamp} |
                <strong>Duration:</strong> ${duration}
            `;

            const stats = document.getElementById('stats');
            stats.innerHTML = `
                <div class="stat">
                    <div class="stat-number">${allResults.length}</div>
                    <div class="stat-label">Total Results</div>
                </div>
                <div class="stat">
                    <div class="stat-number">${data.successfulAccounts || 0}</div>
                    <div class="stat-label">Successful Accounts</div>
                </div>
                <div class="stat">
                    <div class="stat-number">${data.totalAccounts || 0}</div>
                    <div class="stat-label">Total Accounts</div>
                </div>
                <div class="stat">
                    <div class="stat-number">${data.failedAccounts || 0}</div>
                    <div class="stat-label">Failed Accounts</div>
                </div>
            `;
        }
        // Update filter dropdowns
        function updateFilters() {
            const backupFilter = document.getElementById('backupFilter');
            const accountFilter = document.getElementById('accountFilter');

            // Get unique values
            const backups = [...new Set(allResults.map(r => r.backupFolder))].sort();
            const accounts = [...new Set(allResults.map(r => r.accountEmail))].sort();

            // Update backup filter
            backupFilter.innerHTML = '<option value="">All Backups</option>';
            backups.forEach(backup => {
                backupFilter.innerHTML += `<option value="${backup}">${backup}</option>`;
            });

            // Update account filter
            accountFilter.innerHTML = '<option value="">All Accounts</option>';
            accounts.forEach(account => {
                accountFilter.innerHTML += `<option value="${account}">${account}</option>`;
            });
        }

        // Apply filters and search
        function applyFilters() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const backupFilter = document.getElementById('backupFilter').value;
            const accountFilter = document.getElementById('accountFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;

            filteredResults = allResults.filter(result => {
                const matchesSearch = !searchTerm ||
                    (result.name && result.name.toLowerCase().includes(searchTerm)) ||
                    (result.displayName && result.displayName.toLowerCase().includes(searchTerm)) ||
                    (result.id && result.id.toLowerCase().includes(searchTerm)) ||
                    (result.backupFolder && result.backupFolder.toLowerCase().includes(searchTerm));

                const matchesBackup = !backupFilter || result.backupFolder === backupFilter;
                const matchesAccount = !accountFilter || result.accountEmail === accountFilter;
                const matchesType = !typeFilter || result.itemType === typeFilter;

                return matchesSearch && matchesBackup && matchesAccount && matchesType;
            });

            currentPage = 1;
            displayResults();
        }

        // Display paginated results
        function displayResults() {
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageResults = filteredResults.slice(startIndex, endIndex);

            document.getElementById('resultCount').textContent = filteredResults.length;

            const resultsContent = document.getElementById('resultsContent');

            if (pageResults.length === 0) {
                resultsContent.innerHTML = '<div class="no-results">No results match your filters</div>';
                document.getElementById('pagination').innerHTML = '';
                return;
            }

            resultsContent.innerHTML = pageResults.map(result => `
                <div class="result-item">
                    <div class="result-name">${result.displayName || result.name || 'Unknown'}</div>
                    <div class="result-path">${result.id || 'Unknown path'}</div>
                    <div class="result-details">
                        <div class="detail-item">
                            <span class="detail-label">Type:</span>
                            <span class="detail-value">${result.itemType || 'Unknown'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Backup:</span>
                            <span class="detail-value">${result.backupFolder}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Account:</span>
                            <span class="detail-value">${result.accountEmail}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Archive:</span>
                            <span class="detail-value">${result.archiveName}</span>
                        </div>
                    </div>
                </div>
            `).join('');

            updatePagination();
        }

        // Update pagination
        function updatePagination() {
            const totalPages = Math.ceil(filteredResults.length / itemsPerPage);
            const pagination = document.getElementById('pagination');

            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }

            let paginationHTML = '';

            // Previous button
            if (currentPage > 1) {
                paginationHTML += `<button onclick="changePage(${currentPage - 1})">Previous</button>`;
            }

            // Page numbers
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                paginationHTML += `<button onclick="changePage(${i})" ${i === currentPage ? 'class="active"' : ''}>${i}</button>`;
            }

            // Next button
            if (currentPage < totalPages) {
                paginationHTML += `<button onclick="changePage(${currentPage + 1})">Next</button>`;
            }

            pagination.innerHTML = paginationHTML;
        }

        // Change page
        function changePage(page) {
            currentPage = page;
            displayResults();
        }

        // Clear all filters
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('backupFilter').value = '';
            document.getElementById('accountFilter').value = '';
            document.getElementById('typeFilter').value = '';
            applyFilters();
        }

        // Show all searches
        function showAllSearches() {
            const searchList = document.getElementById('searchList');
            const searchSummary = document.getElementById('searchSummary');

            if (allSearches.length === 0) {
                searchList.innerHTML = '<p style="color: #666;">No searches found</p>';
            } else {
                searchList.innerHTML = allSearches.map((search, index) => `
                    <div class="recent-file" onclick="loadSearch(${index})">
                        ${search.timestamp} - ${search.data.totalResults || 0} results
                    </div>
                `).join('');
            }

            searchSummary.style.display = searchSummary.style.display === 'none' ? 'block' : 'none';
        }

        // Load a specific search
        function loadSearch(index) {
            const search = allSearches[index];
            if (search) {
                processLoadedData(search.data, search.filename);
                showStatus(`Loaded search from ${search.timestamp}`, 'success');
            }
        }

        // Event listeners
        document.getElementById('searchInput').addEventListener('input', applyFilters);
        document.getElementById('backupFilter').addEventListener('change', applyFilters);
        document.getElementById('accountFilter').addEventListener('change', applyFilters);
        document.getElementById('typeFilter').addEventListener('change', applyFilters);

        // Initialize
        loadResultsFile();
    </script>
</body>
</html>
